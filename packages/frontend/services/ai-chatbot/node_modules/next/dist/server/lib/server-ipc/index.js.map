{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/index.ts"], "names": ["createIpcServer", "server", "ipcValidationKey", "crypto", "randomBytes", "toString", "ipcServer", "require", "createServer", "req", "res", "url", "URL", "key", "searchParams", "get", "end", "method", "args", "JSON", "parse", "Array", "isArray", "stack", "deserializeErr", "result", "errorToJSON", "stringify", "err", "isError", "code", "console", "error", "name", "message", "ipcPort", "Promise", "resolveIpc", "listen", "hostname", "addr", "address", "port"], "mappings": ";;;;+BASsBA;;;eAAAA;;;wBARM;+DACT;gEACC;8BACW;;;;;;AAKxB,eAAeA,gBACpBC,MAAuC;IAMvC,6EAA6E;IAC7E,yEAAyE;IACzE,6BAA6B;IAC7B,MAAMC,mBAAmBC,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;IAEzD,MAAMC,YAAY,AAACC,QAAQ,QAAkCC,YAAY,CACvE,OAAOC,KAAKC;QACV,IAAI;YACF,MAAMC,MAAM,IAAIC,IAAIH,IAAIE,GAAG,IAAI,KAAK;YACpC,MAAME,MAAMF,IAAIG,YAAY,CAACC,GAAG,CAAC;YAEjC,IAAIF,QAAQX,kBAAkB;gBAC5B,OAAOQ,IAAIM,GAAG;YAChB;YAEA,MAAMC,SAASN,IAAIG,YAAY,CAACC,GAAG,CAAC;YACpC,MAAMG,OAAcC,KAAKC,KAAK,CAACT,IAAIG,YAAY,CAACC,GAAG,CAAC,WAAW;YAE/D,IAAI,CAACE,UAAU,CAACI,MAAMC,OAAO,CAACJ,OAAO;gBACnC,OAAOR,IAAIM,GAAG;YAChB;YAEA,IAAI,OAAO,AAACf,MAAc,CAACgB,OAAO,KAAK,YAAY;oBACHC;gBAA9C,IAAID,WAAW,iCAA+BC,SAAAA,IAAI,CAAC,EAAE,qBAAPA,OAASK,KAAK,GAAE;oBAC5DL,IAAI,CAAC,EAAE,GAAGM,IAAAA,4BAAc,EAACN,IAAI,CAAC,EAAE;gBAClC;gBACA,IAAIO,SAAS,MAAM,AAACxB,MAAc,CAACgB,OAAO,IAAIC;gBAE9C,IAAIO,UAAU,OAAOA,WAAW,YAAYA,OAAOF,KAAK,EAAE;oBACxDE,SAASC,IAAAA,mBAAW,EAACD;gBACvB;gBACAf,IAAIM,GAAG,CAACG,KAAKQ,SAAS,CAACF,UAAU;YACnC;QACF,EAAE,OAAOG,KAAU;YACjB,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,UAAU;gBACzCC,QAAQC,KAAK,CAACJ;YAChB;YACAlB,IAAIM,GAAG,CACLG,KAAKQ,SAAS,CAAC;gBACbC,KAAK;oBAAEK,MAAML,IAAIK,IAAI;oBAAEC,SAASN,IAAIM,OAAO;oBAAEX,OAAOK,IAAIL,KAAK;gBAAC;YAChE;QAEJ;IACF;IAGF,MAAMY,UAAU,MAAM,IAAIC,QAAgB,CAACC;QACzC/B,UAAUgC,MAAM,CAAC,GAAGrC,OAAOsC,QAAQ,EAAE;YACnC,MAAMC,OAAOlC,UAAUmC,OAAO;YAE9B,IAAID,QAAQ,OAAOA,SAAS,UAAU;gBACpCH,WAAWG,KAAKE,IAAI;YACtB;QACF;IACF;IAEA,OAAO;QACLP;QACA7B;QACAJ;IACF;AACF"}