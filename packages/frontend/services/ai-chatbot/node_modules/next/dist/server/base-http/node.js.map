{"version": 3, "sources": ["../../../src/server/base-http/node.ts"], "names": ["NodeNextRequest", "NodeNextResponse", "NEXT_REQUEST_META", "BaseNextRequest", "originalRequest", "_req", "url", "cookies", "value", "constructor", "method", "toUpperCase", "headers", "fetchMetrics", "BaseNextResponse", "originalResponse", "SYMBOL_CLEARED_COOKIES", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end"], "mappings": ";;;;;;;;;;;;;;;IAkBaA,eAAe;eAAfA;;IAwBAC,gBAAgB;eAAhBA;;;0BAvC0B;6BAGL;uBAGkC;IAajEC,qBAAAA,8BAAiB;AAJb,MAAMF,wBAAwBG,sBAAe;IAMlD,IAAIC,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACC,IAAI,CAACH,8BAAiB,CAAC,GAAG,IAAI,CAACA,8BAAiB,CAAC;QACtD,IAAI,CAACG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACD,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACF,IAAI;IAClB;IAEA,IAAID,gBAAgBI,KAAU,EAAE;QAC9B,IAAI,CAACH,IAAI,GAAGG;IACd;IAEAC,YAAY,AAAQJ,IAAS,CAAE;YAjBO;QAkBpC,KAAK,CAACA,KAAKK,MAAM,CAAEC,WAAW,IAAIN,KAAKC,GAAG,EAAGD;aAD3BA,OAAAA;aAlBbO,UAAU,IAAI,CAACP,IAAI,CAACO,OAAO;aAC3BC,gBAA+B,aAAA,IAAI,CAACR,IAAI,qBAAT,WAAWQ,YAAY;YAE7D,CAACX,mBAAkB,GAAgB,IAAI,CAACG,IAAI,CAACH,8BAAiB,CAAC,IAAI,CAAC;IAiBpE;AACF;AAEO,MAAMD,yBAAyBa,uBAAgB;IAKpD,IAAIC,mBAAmB;QACrB,IAAIC,gCAAsB,IAAI,IAAI,EAAE;YAClC,IAAI,CAACC,IAAI,CAACD,gCAAsB,CAAC,GAAG,IAAI,CAACA,gCAAsB,CAAC;QAClE;QAEA,OAAO,IAAI,CAACC,IAAI;IAClB;IAEAR,YACE,AAAQQ,IAA6D,CACrE;QACA,KAAK,CAACA;aAFEA,OAAAA;aAbFC,WAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWf,KAAa,EAAE;QAC5B,IAAI,CAACS,IAAI,CAACM,UAAU,GAAGf;IACzB;IAEA,IAAIgB,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAAchB,KAAa,EAAE;QAC/B,IAAI,CAACS,IAAI,CAACO,aAAa,GAAGhB;IAC5B;IAEAiB,UAAUC,IAAY,EAAElB,KAAwB,EAAQ;QACtD,IAAI,CAACS,IAAI,CAACQ,SAAS,CAACC,MAAMlB;QAC1B,OAAO,IAAI;IACb;IAEAmB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAO,AAACY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAO,AAAD,EAAGI,GAAG,CAAC,CAACzB,QACtDA,MAAM0B,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAElB,KAAa,EAAQ;QAC9C,MAAM+B,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAAChC,QAAQ;YAClC,IAAI,CAACS,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAe/B;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEAiC,KAAKjC,KAAa,EAAE;QAClB,IAAI,CAACU,QAAQ,GAAGV;QAChB,OAAO,IAAI;IACb;IAEAkC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;AACF"}