{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/invoke-request.ts"], "names": ["filterReqHeaders", "ipcForbiddenHeaders", "invokeRequest", "targetUrl", "requestInit", "readableBody", "invokeHeaders", "headers", "fetch", "method", "redirect", "signal", "body", "duplex", "next", "internal"], "mappings": "AAEA,SAASA,gBAAgB,EAAEC,mBAAmB,QAAQ,UAAS;AAE/D,OAAO,MAAMC,gBAAgB,OAC3BC,WACAC,aAKAC;IAEA,MAAMC,gBAAgBN,iBACpB;QACE,iBAAiB;QACjB,GAAGI,YAAYG,OAAO;IACxB,GACAN;IAGF,OAAO,MAAMO,MAAML,WAAW;QAC5BI,SAASD;QACTG,QAAQL,YAAYK,MAAM;QAC1BC,UAAU;QACVC,QAAQP,YAAYO,MAAM;QAE1B,GAAIP,YAAYK,MAAM,KAAK,SAC3BL,YAAYK,MAAM,KAAK,UACvBJ,eACI;YACEO,MAAMP;YACNQ,QAAQ;QACV,IACA,CAAC,CAAC;QAENC,MAAM;YACJ,aAAa;YACbC,UAAU;QACZ;IACF;AACF,EAAC"}