{"version": 3, "sources": ["../../../../../../src/server/future/normalizers/built/pages/pages-filename-normalizer.ts"], "names": ["SERVER_DIRECTORY", "PrefixingNormalizer", "PagesFilenameNormalizer", "constructor", "distDir", "normalize", "manifestFilename"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sCAAqC;AACtE,SAASC,mBAAmB,QAAQ,6BAA4B;AAEhE,OAAO,MAAMC,gCAAgCD;IAC3CE,YAAYC,OAAe,CAAE;QAC3B,KAAK,CAACA,SAASJ;IACjB;IAEOK,UAAUC,gBAAwB,EAAU;QACjD,OAAO,KAAK,CAACD,UAAUC;IACzB;AACF"}