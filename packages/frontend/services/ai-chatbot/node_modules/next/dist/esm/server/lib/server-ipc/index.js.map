{"version": 3, "sources": ["../../../../src/server/lib/server-ipc/index.ts"], "names": ["errorToJSON", "crypto", "isError", "deserializeErr", "createIpcServer", "server", "ipcValidationKey", "randomBytes", "toString", "ipcServer", "require", "createServer", "req", "res", "url", "URL", "key", "searchParams", "get", "end", "method", "args", "JSON", "parse", "Array", "isArray", "stack", "result", "stringify", "err", "code", "console", "error", "name", "message", "ipcPort", "Promise", "resolveIpc", "listen", "hostname", "addr", "address", "port"], "mappings": "AACA,SAASA,WAAW,QAAQ,eAAc;AAC1C,OAAOC,YAAY,SAAQ;AAC3B,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,cAAc,QAAQ,kBAAiB;AAEhD,qDAAqD;AACrD,qDAAqD;AACrD,+CAA+C;AAC/C,OAAO,eAAeC,gBACpBC,MAAuC;IAMvC,6EAA6E;IAC7E,yEAAyE;IACzE,6BAA6B;IAC7B,MAAMC,mBAAmBL,OAAOM,WAAW,CAAC,IAAIC,QAAQ,CAAC;IAEzD,MAAMC,YAAY,AAACC,QAAQ,QAAkCC,YAAY,CACvE,OAAOC,KAAKC;QACV,IAAI;YACF,MAAMC,MAAM,IAAIC,IAAIH,IAAIE,GAAG,IAAI,KAAK;YACpC,MAAME,MAAMF,IAAIG,YAAY,CAACC,GAAG,CAAC;YAEjC,IAAIF,QAAQV,kBAAkB;gBAC5B,OAAOO,IAAIM,GAAG;YAChB;YAEA,MAAMC,SAASN,IAAIG,YAAY,CAACC,GAAG,CAAC;YACpC,MAAMG,OAAcC,KAAKC,KAAK,CAACT,IAAIG,YAAY,CAACC,GAAG,CAAC,WAAW;YAE/D,IAAI,CAACE,UAAU,CAACI,MAAMC,OAAO,CAACJ,OAAO;gBACnC,OAAOR,IAAIM,GAAG;YAChB;YAEA,IAAI,OAAO,AAACd,MAAc,CAACe,OAAO,KAAK,YAAY;oBACHC;gBAA9C,IAAID,WAAW,iCAA+BC,SAAAA,IAAI,CAAC,EAAE,qBAAPA,OAASK,KAAK,GAAE;oBAC5DL,IAAI,CAAC,EAAE,GAAGlB,eAAekB,IAAI,CAAC,EAAE;gBAClC;gBACA,IAAIM,SAAS,MAAM,AAACtB,MAAc,CAACe,OAAO,IAAIC;gBAE9C,IAAIM,UAAU,OAAOA,WAAW,YAAYA,OAAOD,KAAK,EAAE;oBACxDC,SAAS3B,YAAY2B;gBACvB;gBACAd,IAAIM,GAAG,CAACG,KAAKM,SAAS,CAACD,UAAU;YACnC;QACF,EAAE,OAAOE,KAAU;YACjB,IAAI3B,QAAQ2B,QAAQA,IAAIC,IAAI,KAAK,UAAU;gBACzCC,QAAQC,KAAK,CAACH;YAChB;YACAhB,IAAIM,GAAG,CACLG,KAAKM,SAAS,CAAC;gBACbC,KAAK;oBAAEI,MAAMJ,IAAII,IAAI;oBAAEC,SAASL,IAAIK,OAAO;oBAAER,OAAOG,IAAIH,KAAK;gBAAC;YAChE;QAEJ;IACF;IAGF,MAAMS,UAAU,MAAM,IAAIC,QAAgB,CAACC;QACzC5B,UAAU6B,MAAM,CAAC,GAAGjC,OAAOkC,QAAQ,EAAE;YACnC,MAAMC,OAAO/B,UAAUgC,OAAO;YAE9B,IAAID,QAAQ,OAAOA,SAAS,UAAU;gBACpCH,WAAWG,KAAKE,IAAI;YACtB;QACF;IACF;IAEA,OAAO;QACLP;QACA1B;QACAH;IACF;AACF"}