@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  .chat-message {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-lg break-words;
  }
  
  .chat-message-user {
    @apply bg-primary-500 text-white ml-auto;
  }
  
  .chat-message-assistant {
    @apply bg-white text-gray-800 border border-gray-200;
  }
  
  .chat-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none;
  }
  
  .chat-button {
    @apply px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors;
  }
  
  .typing-indicator {
    @apply flex space-x-1;
  }
  
  .typing-dot {
    @apply w-2 h-2 bg-gray-400 rounded-full animate-pulse;
  }
}
