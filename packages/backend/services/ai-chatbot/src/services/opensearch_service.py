"""
OpenSearch service for job search functionality
"""
import os
import json
from typing import List, Dict, Any, Optional
from opensearchpy import OpenSearch
from models.chat_models import JobSearchQuery, JobResult, SearchResults

class OpenSearchService:
    def __init__(self):
        self.client = self._create_client()
        self.index_name = os.getenv("OPENSEARCH_INDEX", "jobs")
    
    def _create_client(self) -> OpenSearch:
        """Create OpenSearch client"""
        host = os.getenv("OPENSEARCH_HOST", "localhost")
        port = int(os.getenv("OPENSEARCH_PORT", "9200"))
        username = os.getenv("OPENSEARCH_USERNAME", "admin")
        password = os.getenv("OPENSEARCH_PASSWORD", "admin")
        use_ssl = os.getenv("OPENSEARCH_USE_SSL", "false").lower() == "true"
        
        return OpenSearch(
            hosts=[{"host": host, "port": port}],
            http_auth=(username, password),
            use_ssl=use_ssl,
            verify_certs=False,
            ssl_assert_hostname=False,
            ssl_show_warn=False,
        )
    
    async def search_jobs(self, query: JobSearchQuery) -> SearchResults:
        """Search for jobs using OpenSearch"""
        try:
            search_body = self._build_search_query(query)
            
            response = self.client.search(
                index=self.index_name,
                body=search_body,
                size=query.size,
                from_=(query.page - 1) * query.size
            )
            
            jobs = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                job = JobResult(
                    id=hit["_id"],
                    title=source.get("title", ""),
                    company=source.get("company", ""),
                    location=source.get("location", ""),
                    description=source.get("description", ""),
                    salary=source.get("salary"),
                    url=source.get("url"),
                    posted_date=source.get("posted_date")
                )
                jobs.append(job)
            
            return SearchResults(
                jobs=jobs,
                total_count=response["hits"]["total"]["value"],
                page=query.page,
                size=query.size,
                query=query.keyword or ""
            )
            
        except Exception as e:
            print(f"Error searching jobs: {e}")
            return SearchResults(
                jobs=[],
                total_count=0,
                page=query.page,
                size=query.size,
                query=query.keyword or ""
            )
    
    def _build_search_query(self, query: JobSearchQuery) -> Dict[str, Any]:
        """Build OpenSearch query from job search parameters"""
        search_body = {
            "query": {
                "bool": {
                    "must": [],
                    "filter": []
                }
            }
        }
        
        # Add keyword search
        if query.keyword:
            search_body["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query.keyword,
                    "fields": ["title^2", "description", "company"],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        
        # Add location filter
        if query.location:
            search_body["query"]["bool"]["filter"].append({
                "match": {
                    "location": query.location
                }
            })
        
        # Add custom filters
        if query.filters:
            for field, value in query.filters.items():
                search_body["query"]["bool"]["filter"].append({
                    "term": {field: value}
                })
        
        # Add sorting
        search_body["sort"] = [
            {"_score": {"order": "desc"}},
            {"posted_date": {"order": "desc", "missing": "_last"}}
        ]
        
        return search_body
    
    async def get_job_suggestions(self, partial_query: str) -> List[str]:
        """Get job title suggestions for autocomplete"""
        try:
            search_body = {
                "suggest": {
                    "job_suggest": {
                        "prefix": partial_query,
                        "completion": {
                            "field": "title_suggest",
                            "size": 5
                        }
                    }
                }
            }
            
            response = self.client.search(
                index=self.index_name,
                body=search_body
            )
            
            suggestions = []
            for option in response["suggest"]["job_suggest"][0]["options"]:
                suggestions.append(option["text"])
            
            return suggestions
            
        except Exception as e:
            print(f"Error getting suggestions: {e}")
            return []
