"""
<PERSON>rip<PERSON> to seed OpenSearch with sample job data for testing
"""
import os
import json
from datetime import datetime, timedelta
import random
from opensearchpy import OpenSearch

# Sample job data
SAMPLE_JOBS = [
    {
        "title": "Senior Software Engineer",
        "company": "TechCorp Inc.",
        "location": "San Francisco, CA",
        "description": "We are looking for a senior software engineer to join our team. Experience with Python, JavaScript, and cloud technologies required.",
        "salary": "$120,000 - $160,000",
        "posted_date": "2024-01-15",
        "skills": ["Python", "JavaScript", "AWS", "Docker"],
        "employment_type": "Full-time",
        "remote": True
    },
    {
        "title": "Data Scientist",
        "company": "DataFlow Analytics",
        "location": "New York, NY",
        "description": "Join our data science team to build machine learning models and analyze large datasets. PhD in Statistics or related field preferred.",
        "salary": "$110,000 - $140,000",
        "posted_date": "2024-01-14",
        "skills": ["Python", "R", "Machine Learning", "SQL"],
        "employment_type": "Full-time",
        "remote": False
    },
    {
        "title": "Frontend Developer",
        "company": "WebDesign Studio",
        "location": "Austin, TX",
        "description": "Create beautiful and responsive web applications using React and modern CSS frameworks.",
        "salary": "$80,000 - $100,000",
        "posted_date": "2024-01-13",
        "skills": ["React", "JavaScript", "CSS", "HTML"],
        "employment_type": "Full-time",
        "remote": True
    },
    {
        "title": "DevOps Engineer",
        "company": "CloudFirst Solutions",
        "location": "Seattle, WA",
        "description": "Manage and optimize our cloud infrastructure. Experience with Kubernetes and CI/CD pipelines required.",
        "salary": "$100,000 - $130,000",
        "posted_date": "2024-01-12",
        "skills": ["Kubernetes", "Docker", "AWS", "Jenkins"],
        "employment_type": "Full-time",
        "remote": True
    },
    {
        "title": "Product Manager",
        "company": "InnovateTech",
        "location": "Boston, MA",
        "description": "Lead product development from conception to launch. Work closely with engineering and design teams.",
        "salary": "$90,000 - $120,000",
        "posted_date": "2024-01-11",
        "skills": ["Product Management", "Agile", "Analytics"],
        "employment_type": "Full-time",
        "remote": False
    },
    {
        "title": "UX Designer",
        "company": "DesignHub",
        "location": "Los Angeles, CA",
        "description": "Design user-centered digital experiences. Portfolio showcasing mobile and web design required.",
        "salary": "$70,000 - $95,000",
        "posted_date": "2024-01-10",
        "skills": ["Figma", "Sketch", "User Research", "Prototyping"],
        "employment_type": "Full-time",
        "remote": True
    },
    {
        "title": "Backend Developer",
        "company": "ServerSide Systems",
        "location": "Chicago, IL",
        "description": "Build scalable backend systems using Node.js and microservices architecture.",
        "salary": "$85,000 - $110,000",
        "posted_date": "2024-01-09",
        "skills": ["Node.js", "MongoDB", "REST APIs", "Microservices"],
        "employment_type": "Full-time",
        "remote": True
    },
    {
        "title": "Marketing Specialist",
        "company": "GrowthMarketing Co.",
        "location": "Miami, FL",
        "description": "Develop and execute marketing campaigns across digital channels. Experience with SEO and social media required.",
        "salary": "$50,000 - $70,000",
        "posted_date": "2024-01-08",
        "skills": ["SEO", "Social Media", "Content Marketing", "Analytics"],
        "employment_type": "Full-time",
        "remote": False
    },
    {
        "title": "Machine Learning Engineer",
        "company": "AI Innovations",
        "location": "Palo Alto, CA",
        "description": "Deploy machine learning models at scale. Experience with TensorFlow and cloud platforms required.",
        "salary": "$130,000 - $170,000",
        "posted_date": "2024-01-07",
        "skills": ["TensorFlow", "Python", "MLOps", "AWS"],
        "employment_type": "Full-time",
        "remote": True
    },
    {
        "title": "Sales Representative",
        "company": "SalesForce Pro",
        "location": "Dallas, TX",
        "description": "Drive revenue growth through B2B sales. Previous SaaS sales experience preferred.",
        "salary": "$60,000 - $80,000 + Commission",
        "posted_date": "2024-01-06",
        "skills": ["B2B Sales", "CRM", "Negotiation"],
        "employment_type": "Full-time",
        "remote": False
    }
]

def create_opensearch_client():
    """Create OpenSearch client"""
    host = os.getenv("OPENSEARCH_HOST", "localhost")
    port = int(os.getenv("OPENSEARCH_PORT", "9200"))
    username = os.getenv("OPENSEARCH_USERNAME", "admin")
    password = os.getenv("OPENSEARCH_PASSWORD", "admin")
    use_ssl = os.getenv("OPENSEARCH_USE_SSL", "false").lower() == "true"
    
    return OpenSearch(
        hosts=[{"host": host, "port": port}],
        http_auth=(username, password),
        use_ssl=use_ssl,
        verify_certs=False,
        ssl_assert_hostname=False,
        ssl_show_warn=False,
    )

def create_jobs_index(client, index_name="jobs"):
    """Create the jobs index with proper mapping"""
    mapping = {
        "mappings": {
            "properties": {
                "title": {
                    "type": "text",
                    "analyzer": "standard",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "company": {
                    "type": "text",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "location": {
                    "type": "text",
                    "fields": {
                        "keyword": {"type": "keyword"}
                    }
                },
                "description": {
                    "type": "text",
                    "analyzer": "standard"
                },
                "salary": {"type": "text"},
                "posted_date": {"type": "date"},
                "skills": {"type": "keyword"},
                "employment_type": {"type": "keyword"},
                "remote": {"type": "boolean"},
                "title_suggest": {
                    "type": "completion",
                    "analyzer": "simple"
                }
            }
        }
    }
    
    # Delete index if it exists
    if client.indices.exists(index=index_name):
        client.indices.delete(index=index_name)
        print(f"Deleted existing index: {index_name}")
    
    # Create new index
    client.indices.create(index=index_name, body=mapping)
    print(f"Created index: {index_name}")

def seed_jobs_data(client, index_name="jobs"):
    """Seed the jobs index with sample data"""
    for i, job in enumerate(SAMPLE_JOBS):
        # Add suggestion field
        job["title_suggest"] = {
            "input": [job["title"], job["title"].lower()],
            "weight": 1
        }
        
        # Add unique ID
        job_id = f"job_{i+1}"
        
        # Index the document
        client.index(
            index=index_name,
            id=job_id,
            body=job
        )
        print(f"Indexed job: {job['title']} at {job['company']}")
    
    # Refresh the index
    client.indices.refresh(index=index_name)
    print(f"Indexed {len(SAMPLE_JOBS)} jobs successfully!")

def main():
    """Main function to seed OpenSearch with job data"""
    try:
        # Create client
        client = create_opensearch_client()
        
        # Test connection
        info = client.info()
        print(f"Connected to OpenSearch: {info['version']['number']}")
        
        # Create index and seed data
        index_name = os.getenv("OPENSEARCH_INDEX", "jobs")
        create_jobs_index(client, index_name)
        seed_jobs_data(client, index_name)
        
        print("✅ OpenSearch seeding completed successfully!")
        
    except Exception as e:
        print(f"❌ Error seeding OpenSearch: {e}")
        raise

if __name__ == "__main__":
    main()
