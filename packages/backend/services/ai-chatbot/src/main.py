"""
AI Chatbot Service with LangGraph-based agentic flow
"""
import os
import uvicorn
from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Any
import json
import asyncio
from datetime import datetime

from agents.chat_agent import Chat<PERSON><PERSON>
from models.chat_models import ChatMessage, ChatResponse
from services.opensearch_service import OpenSearchService

app = FastAPI(title="AI Chatbot Service", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
opensearch_service = OpenSearchService()
chat_agent = ChatAgent(opensearch_service)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_sessions: Dict[str, Dict] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                "websocket": websocket,
                "chat_history": [],
                "created_at": datetime.now()
            }

    def disconnect(self, websocket: WebSocket, user_id: str):
        self.active_connections.remove(websocket)
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_message_to_user(self, message: str, user_id: str):
        if user_id in self.user_sessions:
            websocket = self.user_sessions[user_id]["websocket"]
            await websocket.send_text(message)

manager = ConnectionManager()

@app.get("/")
async def root():
    return {"message": "AI Chatbot Service is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/chat")
async def chat_endpoint(message: ChatMessage):
    """REST endpoint for chat messages"""
    try:
        response = await chat_agent.process_message(
            message.content,
            message.user_id,
            message.session_id
        )
        return ChatResponse(
            content=response["content"],
            message_type=response.get("type", "text"),
            metadata=response.get("metadata", {})
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time chat"""
    print(f"WebSocket connection attempt for user: {user_id}")

    try:
        await manager.connect(websocket, user_id)
        print(f"WebSocket connected for user: {user_id}")

        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                print(f"Received message from {user_id}: {data}")

                message_data = json.loads(data)

                # Process message through agent
                print(f"Processing message through agent...")
                response = await chat_agent.process_message(
                    message_data["content"],
                    user_id,
                    message_data.get("session_id", "default")
                )
                print(f"Agent response: {response}")

                # Send response back to client
                response_json = json.dumps({
                    "content": response["content"],
                    "type": response.get("type", "text"),
                    "metadata": response.get("metadata", {}),
                    "timestamp": datetime.now().isoformat()
                })

                await manager.send_personal_message(response_json, websocket)
                print(f"Sent response to {user_id}")

            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                await manager.send_personal_message(
                    json.dumps({
                        "content": "Invalid message format. Please try again.",
                        "type": "error",
                        "timestamp": datetime.now().isoformat()
                    }),
                    websocket
                )
            except Exception as e:
                print(f"Error processing message: {e}")
                await manager.send_personal_message(
                    json.dumps({
                        "content": f"Error processing message: {str(e)}",
                        "type": "error",
                        "timestamp": datetime.now().isoformat()
                    }),
                    websocket
                )

    except WebSocketDisconnect:
        print(f"WebSocket disconnected for user: {user_id}")
        manager.disconnect(websocket, user_id)
    except Exception as e:
        print(f"WebSocket error for user {user_id}: {e}")
        try:
            await manager.send_personal_message(
                json.dumps({
                    "content": f"Connection error: {str(e)}",
                    "type": "error",
                    "timestamp": datetime.now().isoformat()
                }),
                websocket
            )
        except:
            pass
        manager.disconnect(websocket, user_id)

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
