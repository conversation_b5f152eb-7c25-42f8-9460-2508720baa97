"""
AI Chatbot Service with LangGraph-based agentic flow
"""
import os
import uvicorn
from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Any
import json
import asyncio
from datetime import datetime

from agents.chat_agent import Chat<PERSON><PERSON>
from models.chat_models import ChatMessage, ChatResponse
from services.opensearch_service import OpenSearchService

app = FastAPI(title="AI Chatbot Service", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
opensearch_service = OpenSearchService()
chat_agent = ChatAgent(opensearch_service)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_sessions: Dict[str, Dict] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                "websocket": websocket,
                "chat_history": [],
                "created_at": datetime.now()
            }

    def disconnect(self, websocket: WebSocket, user_id: str):
        self.active_connections.remove(websocket)
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_message_to_user(self, message: str, user_id: str):
        if user_id in self.user_sessions:
            websocket = self.user_sessions[user_id]["websocket"]
            await websocket.send_text(message)

manager = ConnectionManager()

@app.get("/")
async def root():
    return {"message": "AI Chatbot Service is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/chat")
async def chat_endpoint(message: ChatMessage):
    """REST endpoint for chat messages"""
    try:
        response = await chat_agent.process_message(
            message.content, 
            message.user_id, 
            message.session_id
        )
        return ChatResponse(
            content=response["content"],
            message_type=response.get("type", "text"),
            metadata=response.get("metadata", {})
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time chat"""
    await manager.connect(websocket, user_id)
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # Process message through agent
            response = await chat_agent.process_message(
                message_data["content"], 
                user_id, 
                message_data.get("session_id", "default")
            )
            
            # Send response back to client
            await manager.send_personal_message(
                json.dumps({
                    "content": response["content"],
                    "type": response.get("type", "text"),
                    "metadata": response.get("metadata", {}),
                    "timestamp": datetime.now().isoformat()
                }), 
                websocket
            )
            
    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
    except Exception as e:
        await manager.send_personal_message(
            json.dumps({
                "content": f"Error: {str(e)}",
                "type": "error",
                "timestamp": datetime.now().isoformat()
            }), 
            websocket
        )

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
