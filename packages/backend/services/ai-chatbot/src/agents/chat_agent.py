"""
LangGraph-based chat agent for job search conversations
"""
import os
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import <PERSON><PERSON><PERSON>xecutor, ToolInvocation
from pydantic import BaseModel

from services.opensearch_service import OpenSearchService
from models.chat_models import JobSearchQuery, ConversationState, ChatMessage

class AgentState(BaseModel):
    messages: List[Any] = []
    user_id: str = ""
    session_id: str = ""
    last_search_query: Optional[JobSearchQuery] = None
    search_results: Optional[Dict] = None
    intent: str = "general"  # general, job_search, clarification
    context: Dict[str, Any] = {}

class ChatAgent:
    def __init__(self, opensearch_service: OpenSearchService):
        self.opensearch_service = opensearch_service
        self.llm = ChatOpenAI(
            model="gpt-3.5-turbo",
            temperature=0.7,
            api_key=os.getenv("OPENAI_API_KEY")
        )
        self.conversation_states: Dict[str, ConversationState] = {}
        self.graph = self._create_agent_graph()

    def _create_agent_graph(self) -> StateGraph:
        """Create the LangGraph workflow"""

        @tool
        def search_jobs(keyword: str, location: str = "", radius: int = 50, size: int = 10) -> str:
            """Search for jobs based on keyword and location"""
            query = JobSearchQuery(
                keyword=keyword,
                location=location,
                radius=radius,
                size=size
            )
            # Note: This would be async in real implementation
            # For now, we'll handle this in the agent processing
            return f"Searching for {keyword} jobs in {location or 'any location'}"

        @tool
        def get_job_details(job_id: str) -> str:
            """Get detailed information about a specific job"""
            return f"Getting details for job {job_id}"

        tools = [search_jobs, get_job_details]
        tool_executor = ToolExecutor(tools)

        def classify_intent(state: AgentState) -> AgentState:
            """Classify user intent from the message"""
            last_message = state.messages[-1] if state.messages else ""
            message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)

            # Simple intent classification
            job_keywords = ["job", "work", "position", "career", "employment", "hiring", "vacancy"]
            search_keywords = ["search", "find", "look for", "show me"]

            message_lower = message_content.lower()

            if any(keyword in message_lower for keyword in job_keywords + search_keywords):
                state.intent = "job_search"
            else:
                state.intent = "general"

            return state

        def extract_search_params(state: AgentState) -> AgentState:
            """Extract search parameters from user message"""
            if state.intent != "job_search":
                return state

            last_message = state.messages[-1] if state.messages else ""
            message_content = last_message.content if hasattr(last_message, 'content') else str(last_message)

            # Use LLM to extract structured search parameters
            extraction_prompt = f"""
            Extract job search parameters from this message: "{message_content}"

            Return a JSON object with these fields (use null for missing values):
            {{
                "keyword": "job title or skills",
                "location": "city, state or location",
                "radius": 50
            }}

            Only return the JSON object, no other text.
            """

            try:
                response = self.llm.invoke([HumanMessage(content=extraction_prompt)])
                params = json.loads(response.content)

                state.last_search_query = JobSearchQuery(
                    keyword=params.get("keyword"),
                    location=params.get("location"),
                    radius=params.get("radius", 50),
                    size=10
                )
            except Exception as e:
                print(f"Error extracting search params: {e}")
                # Fallback to simple keyword extraction
                state.last_search_query = JobSearchQuery(
                    keyword=message_content,
                    size=10
                )

            return state

        def perform_search(state: AgentState) -> AgentState:
            """Perform job search using OpenSearch"""
            if state.last_search_query:
                try:
                    # Note: In a real implementation, you'd need to handle async properly
                    # For now, we'll create a mock search result
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    results = loop.run_until_complete(
                        self.opensearch_service.search_jobs(state.last_search_query)
                    )
                    loop.close()

                    state.search_results = {
                        "jobs": [job.model_dump() for job in results.jobs],
                        "total_count": results.total_count,
                        "query": results.query
                    }
                except Exception as e:
                    print(f"Error performing search: {e}")
                    state.search_results = {"jobs": [], "total_count": 0, "query": ""}

            return state

        def generate_response(state: AgentState) -> AgentState:
            """Generate conversational response"""
            if state.intent == "job_search" and state.search_results:
                response = self._format_job_search_response(state.search_results, state.last_search_query)
            else:
                response = self._generate_general_response(state.messages)

            state.messages.append(AIMessage(content=response))
            return state

        def should_search(state: AgentState) -> str:
            """Decide whether to perform a job search"""
            return "search" if state.intent == "job_search" else "respond"

        # Build the graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("classify_intent", classify_intent)
        workflow.add_node("extract_params", extract_search_params)
        workflow.add_node("search", perform_search)
        workflow.add_node("respond", generate_response)

        # Add edges
        workflow.set_entry_point("classify_intent")
        workflow.add_edge("classify_intent", "extract_params")
        workflow.add_conditional_edges(
            "extract_params",
            should_search,
            {
                "search": "search",
                "respond": "respond"
            }
        )
        workflow.add_edge("search", "respond")
        workflow.add_edge("respond", END)

        return workflow.compile()

    async def process_message(self, message: str, user_id: str, session_id: str) -> Dict[str, Any]:
        """Process a user message through the agent"""
        # Get or create conversation state
        state_key = f"{user_id}_{session_id}"
        if state_key not in self.conversation_states:
            self.conversation_states[state_key] = ConversationState(
                user_id=user_id,
                session_id=session_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

        conv_state = self.conversation_states[state_key]
        conv_state.messages.append(ChatMessage(
            content=message,
            user_id=user_id,
            session_id=session_id,
            timestamp=datetime.now()
        ))

        # Create agent state
        agent_state = AgentState(
            messages=[HumanMessage(content=message)],
            user_id=user_id,
            session_id=session_id
        )

        # Process through the graph
        result = await self.graph.ainvoke(agent_state)

        # Extract response
        last_message = result["messages"][-1]
        response_content = last_message.content

        # Update conversation state
        conv_state.updated_at = datetime.now()
        if result.get("last_search_query"):
            conv_state.last_search_query = result["last_search_query"]
        if result.get("search_results"):
            conv_state.last_search_results = result["search_results"]

        return {
            "content": response_content,
            "type": "text",
            "metadata": {
                "intent": result.get("intent", "general"),
                "search_performed": bool(result.get("search_results")),
                "job_count": len(result.get("search_results", {}).get("jobs", []))
            }
        }

    def _format_job_search_response(self, search_results: Dict, query: JobSearchQuery) -> str:
        """Format job search results into a conversational response"""
        jobs = search_results.get("jobs", [])
        total_count = search_results.get("total_count", 0)

        if not jobs:
            return f"I couldn't find any jobs matching '{query.keyword}'. Try adjusting your search terms or location."

        response = f"I found {total_count} jobs"
        if query.keyword:
            response += f" for '{query.keyword}'"
        if query.location:
            response += f" in {query.location}"
        response += ". Here are the top results:\n\n"

        for i, job in enumerate(jobs[:5], 1):
            response += f"{i}. **{job['title']}** at {job['company']}\n"
            response += f"   📍 {job['location']}\n"
            if job.get('salary'):
                response += f"   💰 {job['salary']}\n"
            response += f"   {job['description'][:100]}...\n\n"

        if total_count > 5:
            response += f"And {total_count - 5} more jobs available. Would you like to see more or refine your search?"

        return response

    def _generate_general_response(self, messages: List) -> str:
        """Generate a general conversational response"""
        system_prompt = """You are a helpful job search assistant. You can help users find jobs,
        provide career advice, and answer questions about employment. Be friendly and professional."""

        conversation = [SystemMessage(content=system_prompt)] + messages
        response = self.llm.invoke(conversation)
        return response.content
