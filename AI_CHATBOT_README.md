# AI Job Search Chatbot

A modern AI-powered chatbot with a simple chat frontend interface and LangGraph-based agentic flow that integrates with OpenSearch for job search functionality.

## Architecture

### Backend (`packages/backend/services/ai-chatbot`)
- **FastAPI** application with WebSocket support for real-time chat
- **LangGraph** agentic workflow for intelligent conversation handling
- **OpenSearch** integration for job search functionality
- **OpenAI GPT** for natural language processing and response generation

### Frontend (`packages/frontend/services/ai-chatbot`)
- **Next.js 14** with App Router
- **Tailwind CSS** for styling
- **WebSocket** connection for real-time messaging
- **Responsive design** with typing indicators and message history

## Features

- 🤖 **Intelligent Conversation**: LangGraph-based agent that understands job search intent
- 🔍 **Job Search**: Integration with OpenSearch/Elasticsearch for job queries
- 💬 **Real-time Chat**: WebSocket-based messaging with typing indicators
- 📱 **Responsive UI**: Clean, modern interface that works on all devices
- 🎯 **Intent Classification**: Automatically detects job search vs. general conversation
- 📊 **Search Results Formatting**: Conversational presentation of job search results

## Quick Start

### Prerequisites

- Docker and Docker Compose
- OpenAI API key
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Using Docker Compose (Recommended)

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/talent/sandbox
   ```

2. **Set up environment variables**:
   ```bash
   # Create .env file in the root directory
   echo "OPENAI_API_KEY=your_openai_api_key_here" > .env
   ```

3. **Start all services**:
   ```bash
   docker-compose -f docker-compose.chatbot.yml up -d
   ```

4. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - OpenSearch: http://localhost:9200
   - OpenSearch Dashboards: http://localhost:5601

### Local Development Setup

#### Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd packages/backend/services/ai-chatbot
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Start the backend**:
   ```bash
   python src/main.py
   ```

#### Frontend Setup

1. **Navigate to frontend directory**:
   ```bash
   cd packages/frontend/services/ai-chatbot
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start the frontend**:
   ```bash
   npm run dev
   ```

## Configuration

### Backend Environment Variables

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# OpenSearch Configuration
OPENSEARCH_HOST=localhost
OPENSEARCH_PORT=9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=admin
OPENSEARCH_USE_SSL=false
OPENSEARCH_INDEX=jobs

# Application Configuration
PORT=8000
DEBUG=true
```

### Frontend Environment Variables

```env
# Chatbot API Configuration
NEXT_PUBLIC_CHATBOT_API_URL=http://localhost:8000
NEXT_PUBLIC_CHATBOT_WS_URL=ws://localhost:8000
```

## API Endpoints

### REST API

- `GET /` - Health check
- `GET /health` - Detailed health status
- `POST /api/chat` - Send chat message (REST)

### WebSocket

- `WS /ws/{user_id}` - Real-time chat connection

## LangGraph Agent Flow

The chatbot uses a sophisticated LangGraph workflow:

1. **Intent Classification**: Determines if the message is job-search related
2. **Parameter Extraction**: Extracts search parameters (keywords, location, etc.)
3. **Conditional Routing**: Routes to search or general conversation
4. **Job Search**: Queries OpenSearch for relevant jobs
5. **Response Generation**: Formats results into conversational responses

## Usage Examples

### Job Search Queries
- "Find software engineer jobs in San Francisco"
- "Show me remote Python developer positions"
- "I'm looking for marketing jobs near me"

### General Conversation
- "What skills should I learn for data science?"
- "How do I write a good resume?"
- "Tell me about the job market"

## Testing

### Backend Tests
```bash
cd packages/backend/services/ai-chatbot
python -m pytest tests/
```

### Frontend Tests
```bash
cd packages/frontend/services/ai-chatbot
npm test
```

## Deployment

### Production Docker Build

```bash
# Build backend
docker build -t chatbot-backend packages/backend/services/ai-chatbot/

# Build frontend
docker build -t chatbot-frontend packages/frontend/services/ai-chatbot/
```

### Environment-specific Configuration

Update the docker-compose file or Kubernetes manifests with production-ready configurations:

- Use proper OpenSearch credentials
- Configure CORS for production domains
- Set up proper logging and monitoring
- Use production OpenAI API limits

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**:
   - Check if backend is running on port 8000
   - Verify CORS configuration
   - Check browser console for errors

2. **OpenSearch Connection Error**:
   - Ensure OpenSearch is running and accessible
   - Verify credentials and host configuration
   - Check if the jobs index exists

3. **OpenAI API Errors**:
   - Verify API key is correct and has sufficient credits
   - Check rate limits and quotas
   - Monitor API usage in OpenAI dashboard

### Logs

- Backend logs: Check Docker container logs or console output
- Frontend logs: Check browser developer console
- OpenSearch logs: Check OpenSearch container logs

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is part of the Talent platform and follows the same licensing terms.
