{"classifiers": ["License :: OSI Approved :: MIT License", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "Ville Vainio", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/pickleshare/pickleshare"}}}, "extras": [], "generator": "bdist_wheel (0.29.0)", "keywords": ["database", "persistence", "pickle", "ipc", "shelve"], "license": "MIT", "metadata_version": "2.0", "name": "pickleshare", "run_requires": [{"environment": "python_version in \"2.6 2.7 3.2 3.3\"", "requires": ["pathlib2"]}], "summary": "Tiny 'shelve'-like database with concurrency support", "version": "0.7.5"}