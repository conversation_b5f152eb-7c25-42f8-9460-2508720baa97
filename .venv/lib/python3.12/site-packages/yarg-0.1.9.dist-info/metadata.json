{"license": "MIT", "name": "yarg", "metadata_version": "2.0", "generator": "bdist_wheel (0.24.0)", "summary": "A semi hard Cornish cheese, also queries PyPI (PyPI client)", "platform": "linux", "run_requires": [{"requires": ["requests"]}], "version": "0.1.9", "extensions": {"python.details": {"project_urls": {"Home": "https://yarg.readthedocs.org/"}, "document_names": {"description": "DESCRIPTION.rst"}, "contacts": [{"role": "author", "email": "<EMAIL>", "name": "<PERSON><PERSON>"}]}}, "provides": "yarg", "keywords": ["pypi", "client", "packages"], "requires": "requests", "classifiers": ["Development Status :: 4 - Beta", "Intended Audience :: <PERSON><PERSON><PERSON>", "Natural Language :: English", "License :: OSI Approved :: MIT License", "Programming Language :: Python", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: Implementation :: CPython", "Programming Language :: Python :: Implementation :: PyPy", "Topic :: Software Development :: Libraries :: Python Modules", "Topic :: System :: Archiving :: Packaging"], "extras": []}