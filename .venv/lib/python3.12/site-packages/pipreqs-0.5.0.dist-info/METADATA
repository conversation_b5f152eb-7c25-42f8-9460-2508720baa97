Metadata-Version: 2.1
Name: pipreqs
Version: 0.5.0
Summary: Pip requirements.txt generator based on imports in project
Home-page: https://github.com/bndr/pipreqs
License: Apache-2.0
Keywords: pip,requirements,imports
Author: <PERSON><PERSON><PERSON>
Author-email: vadim.<PERSON>ra<PERSON><PERSON><PERSON>@gmail.com
Requires-Python: >=3.8.1,<3.13
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.8
Requires-Dist: docopt (==0.6.2)
Requires-Dist: ipython (==8.12.3)
Requires-Dist: nbconvert (>=7.11.0,<8.0.0)
Requires-Dist: yarg (==0.1.9)
Project-URL: Repository, https://github.com/bndr/pipreqs
Description-Content-Type: text/x-rst

=============================================================================
``pipreqs`` - Generate requirements.txt file for any project based on imports
=============================================================================

.. image:: https://img.shields.io/travis/bndr/pipreqs.svg
        :target: https://travis-ci.org/bndr/pipreqs


.. image:: https://img.shields.io/pypi/v/pipreqs.svg
        :target: https://pypi.python.org/pypi/pipreqs


.. image:: https://codecov.io/gh/bndr/pipreqs/branch/master/graph/badge.svg?token=0rfPfUZEAX
        :target: https://codecov.io/gh/bndr/pipreqs

.. image:: https://img.shields.io/pypi/l/pipreqs.svg
        :target: https://pypi.python.org/pypi/pipreqs



Installation
------------

.. code-block:: sh

    pip install pipreqs

Obs.: if you don't want support for jupyter notebooks, you can install pipreqs without the dependencies that give support to it. 
To do so, run:

.. code-block:: sh

    pip install --no-deps pipreqs
    pip install yarg==0.1.9 docopt==0.6.2

Usage
-----

::

    Usage:
        pipreqs [options] [<path>]

    Arguments:
        <path>                The path to the directory containing the application files for which a requirements file
                              should be generated (defaults to the current working directory)

    Options:
        --use-local           Use ONLY local package info instead of querying PyPI
        --pypi-server <url>   Use custom PyPi server
        --proxy <url>         Use Proxy, parameter will be passed to requests library. You can also just set the
                              environments parameter in your terminal:
                              $ export HTTP_PROXY="http://**********:3128"
                              $ export HTTPS_PROXY="https://**********:1080"
        --debug               Print debug information
        --ignore <dirs>...    Ignore extra directories, each separated by a comma
        --no-follow-links     Do not follow symbolic links in the project
        --encoding <charset>  Use encoding parameter for file open
        --savepath <file>     Save the list of requirements in the given file
        --print               Output the list of requirements in the standard output
        --force               Overwrite existing requirements.txt
        --diff <file>         Compare modules in requirements.txt to project imports
        --clean <file>        Clean up requirements.txt by removing modules that are not imported in project
        --mode <scheme>       Enables dynamic versioning with <compat>, <gt> or <non-pin> schemes
                              <compat> | e.g. Flask~=1.1.2
                              <gt>     | e.g. Flask>=1.1.2
                              <no-pin> | e.g. Flask
        --scan-notebooks      Look for imports in jupyter notebook files.

Example
-------

::

    $ pipreqs /home/<USER>/location
    Successfully saved requirements file in /home/<USER>/location/requirements.txt

Contents of requirements.txt

::

    wheel==0.23.0
    Yarg==0.1.9
    docopt==0.6.2

Why not pip freeze?
-------------------

- ``pip freeze`` only saves the packages that are installed with ``pip install`` in your environment.
- ``pip freeze`` saves all packages in the environment including those that you don't use in your current project (if you don't have ``virtualenv``).
- and sometimes you just need to create ``requirements.txt`` for a new project without installing modules.

.. :changelog:

History
-------

0.4.11 (2020-03-29)
--------------------

* Implement '--mode' (Jake Teo, Jerome Chan)

0.4.8 (2017-06-30)
--------------------

* Implement '--clean' and '--diff' (kxrd)
* Exclude concurrent{,.futures} from stdlib if py2 (kxrd)

0.4.7 (2017-04-20)
--------------------

* BUG: remove package/version duplicates
* Style: pep8

0.4.5 (2016-12-13)
---------------------

* Fixed the --pypi-server option

0.4.4 (2016-07-14)
---------------------

* Remove Spaces in output
* Add package to output even without version

0.4.2 (2016-02-10)
---------------------

* Fix duplicated lines in requirements.txt (Dmitry Pribysh)

0.4.1 (2016-02-05)
---------------------

* Added ignore option (Nick Rhinehart)

0.4.0 (2016-01-28)
---------------------

* Walk Abstract Syntax Tree to find imports (Kay Sackey)

0.3.9 (2016-01-20)
---------------------

* Fix regex for docstring comments (#35)

0.3.8 (2016-01-12)
---------------------

* Add more package mapping
* fix(pipreqs/mapping): remove pylab reference to matplotlib
* Remove comments """ before going through imports
* Update proxy documentation

0.3.1 (2015-10-20)
---------------------

* fixed lint warnings (EJ Lee)
* add --encoding parameter for open() (EJ Lee)
* support windows directory separator (EJ Lee)

0.3.0 (2015-09-29)
---------------------

* Add --proxy option
* Add --pypi-server option

0.2.9 (2015-09-24)
---------------------

* Ignore irreverent directory when generating requirement.txt (Lee Wei)
* Modify logging level of "Requirement.txt already exists" to warning (Lee Wei)

0.2.8 (2015-05-11)
---------------------

* Add --force option as a protection for overwrites

0.2.6 (2015-05-11)
---------------------

* Fix exception when 'import' is used inside package name #17
* Add more tests

0.2.5 (2015-05-11)
---------------------

* Fix exception when 'import' is used in comments #17
* Fix duplicate entries in requirements.txt

0.2.4 (2015-05-10)
---------------------

* Refactoring
* fix "import as"

0.2.3 (2015-05-09)
---------------------

* Fix multiple alias imports on the same line (Tiago Costa)
* More package mappings

0.2.2 (2015-05-08)
---------------------

* Add ImportName -> PackageName mapping
* More tests

0.2.1 (2015-05-08)
---------------------

* Fix for TypeError for implicit conversion

0.2.0 (2015-05-06)
---------------------

* Add --use-local option
* Exclude relative imports. (Dongwon Shin)
* Use "latest_release_id" instead of "release_ids[-1]" (Dongwon Shin)

0.1.9 (2015-05-01)
---------------------

* Output tuning (Harri Berglund)
* Use str.partition() to simplify the logic (cclaus)

0.1.8 (2015-04-26)
---------------------

* Fixed problems with local imports (Dongwon Shin)
* Fixed problems with imports with 'as' (Dongwon Shin)
* Fix indentation, pep8 Styling. (Michael Borisov)
* Optimize imports and adding missing import for sys module. (Michael Borisov)

0.1.7 (2015-04-24)
---------------------

* Add more assertions in tests
* Add more verbose output
* Add recursive delete to Makefile clean
* Update Readme

0.1.6 (2015-04-22)
---------------------

* py3 print function

0.1.5 (2015-04-22)
---------------------

* Add Readme, Add Examples
* Add Stdlib into package

0.1.1 (2015-04-22)
---------------------

* Fix regex matching for imports
* Release on Pypi

0.1.0 (2015-04-22)
---------------------

* First release on Github.

