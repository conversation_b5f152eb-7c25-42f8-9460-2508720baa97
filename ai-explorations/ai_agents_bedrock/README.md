# Job Search Agent for Amazon Bedrock

## Overview

This project demonstrates how to create an Amazon Bedrock agent for job search functionality. The agent uses a knowledge base containing job listings and can answer natural language queries about available jobs.

The implementation showcases several key Amazon Bedrock capabilities:
- Creating and configuring Bedrock agents
- Setting up knowledge bases with document ingestion
- Associating knowledge bases with agents
- Testing and interacting with agents

## Prerequisites

- AWS account with access to Amazon Bedrock
- AWS CLI configured with appropriate permissions
- Python 3.8+ installed
- Required Python packages: boto3, uuid, argparse

## Project Structure

```
.
├── job_search_agent.py    # Main script for creating the agent
├── knowledge_base.py            # Helper module for knowledge base operations
└── kb_documents/                # Directory containing job listing documents
    ├── job1.txt
    ├── job2.txt
    └── ...
```

## Setup

1. Ensure you have the necessary AWS credentials configured:
   ```bash
   aws configure
   ```

2. Install required Python packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Prepare your job listing documents in the `kb_documents` directory. Each document should contain information about a job listing.

## Usage

### Basic Usage

To create a job search agent with default settings:

```bash
python job_search_agent.py
```

This will:
1. Create a Bedrock agent named "jobsearch-agent"
2. Set up a knowledge base with documents from the "kb_documents" directory
3. Associate the knowledge base with the agent
4. Create an agent alias
5. Run a test query against the agent

### Interactive Mode

To create an agent and then interact with it through multiple queries:

```bash
python job_search_agent.py --interactive
```

After the agent is created, you'll be prompted to enter queries. Type "exit" or "quit" to end the session.

### Custom Configuration

You can customize various aspects of the agent:

```bash
python job_search_agent.py \
  --agent-name "custom-job-agent" \
  --agent-description "Custom job search assistant" \
  --agent-foundation-model "anthropic.claude-3-sonnet-20240229-v1:0" \
  --kb-documents-path "custom_kb_docs" \
  --alias-id "MYALIAS" \
  --test-query "Find software engineering jobs in Seattle" \
  --interactive
```

## Command-Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--agent-name` | Name of the agent | "jobsearch-agent" |
| `--agent-description` | Description of the agent | "Agent in charge of job search" |
| `--agent-foundation-model` | Foundation model ID | "anthropic.claude-3-sonnet-20240229-v1:0" |
| `--kb-documents-path` | Path to knowledge base documents | "kb_documents" |
| `--alias-id` | Alias ID for the agent | "TSTALIASID" |
| `--test-query` | Test query to run against the agent | None |
| `--interactive` | Run in interactive mode after creation | False |

## Operational Modes

The script supports three operational modes:

### 1. Create Mode

Create a new agent and knowledge base:

```bash
python job_search_agent.py --create --agent-name "my-job-search-agent"
```

This will:
- Create a new agent and knowledge base
- Save the state (agent ID, alias ID, KB ID) to a JSON file
- Optionally run a test query or interactive session

### 2. Query Mode

Query an existing agent:

```bash
python job_search_agent.py --query --agent-name "my-job-search-agent" --interactive
```

This will:
- Load the agent state from the saved JSON file
- Run a test query or interactive session with the existing agent
- No new resources are created

### 3. Delete Mode

Delete an existing agent and all associated resources:

```bash
python job_search_agent.py --delete --agent-name "my-job-search-agent"
```

This will:
- Load the agent state from the saved JSON file
- Delete all resources (agent, alias, knowledge base, S3 bucket, IAM roles)
- Remove the state file if cleanup is successful

## State Management

The script saves the state (agent ID, alias ID, KB ID) to a JSON file named `{agent_name}_state.json`. This allows you to:

1. Create an agent once and query it multiple times
2. Delete the agent when you're done
3. Avoid creating duplicate resources

The state file is automatically created in create mode and used in query and delete modes.

## How It Works

1. **Agent Creation**: The script creates a Bedrock agent with specified parameters and foundation model.

2. **Knowledge Base Setup**: It creates a knowledge base, uploads documents from the specified directory, and associates the knowledge base with the agent.

3. **Agent Preparation**: The agent is prepared with the new knowledge base, which involves processing the documents and making them available for queries.

4. **Alias Creation**: An alias is created for the agent, which is required for invoking the agent.

5. **Testing**: The script runs a test query against the agent to verify functionality.

6. **Interactive Mode**: If enabled, the script enters an interactive mode where users can input multiple queries.

## Example Queries

- "What software engineering jobs are available?"
- "Find me marketing positions in New York"
- "Are there any remote data science jobs?"
- "Tell me about entry-level positions in finance"
- "What jobs require Python experience?"

## Cleaning Up Resources

To avoid incurring charges, you can clean up all resources created by this script:

### Using the --cleanup Flag

The simplest way to clean up is to add the `--cleanup` flag when running the script:

```bash
python job_search_agent.py --cleanup
```

This will:
1. Create the agent and knowledge base
2. Test the agent
3. Automatically delete all resources when done

### Manual Cleanup

You can also clean up resources programmatically:

```python
from job_search_agent import cleanup_resources

# Clean up all resources
cleanup_resources(agent_id, alias_id, kb_id, agent_name)
```

### What Gets Cleaned Up

The cleanup process removes:
1. Agent alias
2. Agent
3. Knowledge base and data sources
4. S3 bucket and contents
5. IAM roles and policies created for the agent and knowledge base

### Verifying Cleanup

After cleanup, you can verify that resources were deleted by checking:
- The Amazon Bedrock console for agents and knowledge bases
- The S3 console for buckets
- The IAM console for roles and policies

## Troubleshooting

- **Permission Issues**: Ensure your AWS credentials have the necessary permissions for Bedrock, IAM, and S3.
- **Model Access**: Verify you have access to the specified foundation model in your AWS account.
- **Document Format**: Ensure your job listing documents are in a format that can be processed by the knowledge base.

## Further Resources

- [Amazon Bedrock Documentation](https://docs.aws.amazon.com/bedrock/)
- [Agents for Amazon Bedrock](https://docs.aws.amazon.com/bedrock/latest/userguide/agents.html)
- [Knowledge Bases for Amazon Bedrock](https://docs.aws.amazon.com/bedrock/latest/userguide/knowledge-base.html)
