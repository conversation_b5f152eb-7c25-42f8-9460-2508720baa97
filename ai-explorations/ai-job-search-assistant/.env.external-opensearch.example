# External OpenSearch Configuration Template
# Copy this file to .env and configure your external OpenSearch cluster

# =============================================================================
# OpenAI Configuration (Required)
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# External OpenSearch Configuration
# =============================================================================

# Option 1: Full URL (Recommended)
# Use this for AWS OpenSearch Service or any HTTPS endpoint
OPENSEARCH_URL=https://your-opensearch-domain.us-east-1.es.amazonaws.com

# Option 2: Individual Components (Alternative)
# Use these if you prefer to specify host/port separately
# OPENSEARCH_HOST=your-opensearch-host.com
# OPENSEARCH_PORT=443
# OPENSEARCH_USE_SSL=true

# =============================================================================
# Authentication Configuration (Choose One)
# =============================================================================

# Basic Authentication (Username/Password)
OPENSEARCH_USERNAME=your_username
OPENSEARCH_PASSWORD=your_password

# API Key Authentication (Alternative to basic auth)
# OPENSEARCH_API_KEY=your_api_key_here

# AWS IAM Authentication (For AWS OpenSearch Service)
# OPENSEARCH_USE_AWS_AUTH=true
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_SESSION_TOKEN=your_aws_session_token  # Optional, for temporary credentials

# =============================================================================
# OpenSearch Settings
# =============================================================================

# Index name containing job data
OPENSEARCH_INDEX=jobs

# SSL/TLS Settings
OPENSEARCH_VERIFY_CERTS=true

# Connection timeout (seconds)
OPENSEARCH_TIMEOUT=30

# =============================================================================
# Example Configurations
# =============================================================================

# Example 1: AWS OpenSearch Service with IAM
# OPENSEARCH_URL=https://search-your-domain.us-east-1.es.amazonaws.com
# OPENSEARCH_USE_AWS_AUTH=true
# AWS_REGION=us-east-1
# OPENSEARCH_INDEX=jobs

# Example 2: Self-hosted OpenSearch with basic auth
# OPENSEARCH_URL=https://opensearch.yourcompany.com:9200
# OPENSEARCH_USERNAME=admin
# OPENSEARCH_PASSWORD=your_secure_password
# OPENSEARCH_INDEX=jobs

# Example 3: Local OpenSearch (development)
# OPENSEARCH_URL=http://localhost:9200
# OPENSEARCH_USERNAME=admin
# OPENSEARCH_PASSWORD=admin
# OPENSEARCH_INDEX=jobs
# OPENSEARCH_VERIFY_CERTS=false
