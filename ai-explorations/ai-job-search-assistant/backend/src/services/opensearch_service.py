"""
OpenSearch service for job search functionality
"""
import os
import json
from typing import List, Dict, Any, Optional
from opensearchpy import OpenSearch
from models.chat_models import JobSearch<PERSON><PERSON>y, JobResult, SearchResults

class OpenSearchService:
    def __init__(self):
        self.client = None
        self.index_name = os.getenv("OPENSEARCH_INDEX", "jobs")
        self._connection_attempted = False

    def _create_client(self) -> OpenSearch:
        """Create OpenSearch client with support for external clusters"""
        # Support both URL format and host/port format
        opensearch_url = os.getenv("OPENSEARCH_URL")
        if opensearch_url:
            # Parse URL format (e.g., https://search-domain.us-east-1.es.amazonaws.com)
            from urllib.parse import urlparse
            parsed = urlparse(opensearch_url)
            host = parsed.hostname
            port = parsed.port or (443 if parsed.scheme == 'https' else 9200)
            use_ssl = parsed.scheme == 'https'
        else:
            # Use individual environment variables
            host = os.getenv("OPENSEARCH_HOST", "localhost")
            port = int(os.getenv("OPENSEARCH_PORT", "9200"))
            use_ssl = os.getenv("OPENSEARCH_USE_SSL", "false").lower() == "true"

        # Authentication options
        username = os.getenv("OPENSEARCH_USERNAME")
        password = os.getenv("OPENSEARCH_PASSWORD")
        api_key = os.getenv("OPENSEARCH_API_KEY")

        # AWS IAM authentication
        use_aws_auth = os.getenv("OPENSEARCH_USE_AWS_AUTH", "false").lower() == "true"
        aws_region = os.getenv("AWS_REGION", "us-east-1")

        # Connection settings
        verify_certs = os.getenv("OPENSEARCH_VERIFY_CERTS", "true").lower() == "true"
        timeout = int(os.getenv("OPENSEARCH_TIMEOUT", "30"))

        try:
            connection_params = {
                "hosts": [{"host": host, "port": port}],
                "use_ssl": use_ssl,
                "verify_certs": verify_certs,
                "ssl_assert_hostname": False,
                "ssl_show_warn": False,
                "timeout": timeout,
                "max_retries": 3,
                "retry_on_timeout": True
            }

            # Add authentication
            if use_aws_auth:
                # AWS IAM authentication
                try:
                    import boto3
                    from requests_aws4auth import AWS4Auth

                    credentials = boto3.Session().get_credentials()
                    awsauth = AWS4Auth(
                        credentials.access_key,
                        credentials.secret_key,
                        aws_region,
                        'es',
                        session_token=credentials.token
                    )
                    connection_params["http_auth"] = awsauth
                    print(f"Using AWS IAM authentication for OpenSearch")
                except ImportError:
                    print("Warning: boto3 and requests-aws4auth required for AWS authentication")
                    return None
                except Exception as e:
                    print(f"Warning: AWS authentication failed: {e}")
                    return None
            elif api_key:
                # API Key authentication
                connection_params["api_key"] = api_key
                print(f"Using API key authentication for OpenSearch")
            elif username and password:
                # Basic authentication
                connection_params["http_auth"] = (username, password)
                print(f"Using basic authentication for OpenSearch")
            else:
                print("Warning: No authentication method configured for OpenSearch")

            print(f"Connecting to OpenSearch at {host}:{port} (SSL: {use_ssl})")
            client = OpenSearch(**connection_params)

            # Test connection
            info = client.info()
            print(f"✅ Connected to OpenSearch: {info['version']['number']}")
            return client

        except Exception as e:
            print(f"❌ Could not connect to OpenSearch: {e}")
            print(f"   Host: {host}:{port}, SSL: {use_ssl}")
            return None

    async def search_jobs(self, query: JobSearchQuery) -> SearchResults:
        """Search for jobs using OpenSearch or mock data"""
        # Lazy initialization of OpenSearch client
        if self.client is None and not self._connection_attempted:
            self.client = self._create_client()
            self._connection_attempted = True

        # If OpenSearch is not available, use mock data
        if self.client is None:
            return self._get_mock_search_results(query)

        try:
            search_body = self._build_search_query(query)

            response = self.client.search(
                index=self.index_name,
                body=search_body,
                size=query.size,
                from_=(query.page - 1) * query.size
            )

            jobs = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                job = JobResult(
                    id=hit["_id"],
                    title=source.get("title", ""),
                    company=source.get("company", ""),
                    location=source.get("location", ""),
                    description=source.get("description", ""),
                    salary=source.get("salary"),
                    url=source.get("url"),
                    posted_date=source.get("posted_date")
                )
                jobs.append(job)

            return SearchResults(
                jobs=jobs,
                total_count=response["hits"]["total"]["value"],
                page=query.page,
                size=query.size,
                query=query.keyword or ""
            )

        except Exception as e:
            print(f"Error searching jobs: {e}")
            return self._get_mock_search_results(query)

    def _build_search_query(self, query: JobSearchQuery) -> Dict[str, Any]:
        """Build OpenSearch query from job search parameters"""
        search_body = {
            "query": {
                "bool": {
                    "must": [],
                    "filter": []
                }
            }
        }

        # Add keyword search
        if query.keyword:
            search_body["query"]["bool"]["must"].append({
                "multi_match": {
                    "query": query.keyword,
                    "fields": ["title^2", "description", "company"],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })

        # Add location filter
        if query.location:
            search_body["query"]["bool"]["filter"].append({
                "match": {
                    "location": query.location
                }
            })

        # Add custom filters
        if query.filters:
            for field, value in query.filters.items():
                search_body["query"]["bool"]["filter"].append({
                    "term": {field: value}
                })

        # Add sorting
        search_body["sort"] = [
            {"_score": {"order": "desc"}},
            {"posted_date": {"order": "desc", "missing": "_last"}}
        ]

        return search_body

    async def get_job_suggestions(self, partial_query: str) -> List[str]:
        """Get job title suggestions for autocomplete"""
        try:
            search_body = {
                "suggest": {
                    "job_suggest": {
                        "prefix": partial_query,
                        "completion": {
                            "field": "title_suggest",
                            "size": 5
                        }
                    }
                }
            }

            response = self.client.search(
                index=self.index_name,
                body=search_body
            )

            suggestions = []
            for option in response["suggest"]["job_suggest"][0]["options"]:
                suggestions.append(option["text"])

            return suggestions

        except Exception as e:
            print(f"Error getting suggestions: {e}")
            return []

    def _get_mock_search_results(self, query: JobSearchQuery) -> SearchResults:
        """Return mock search results when OpenSearch is not available"""
        mock_jobs = [
            JobResult(
                id="mock_1",
                title="Senior Software Engineer",
                company="TechCorp Inc.",
                location="San Francisco, CA",
                description="We are looking for a senior software engineer to join our team. Experience with Python, JavaScript, and cloud technologies required.",
                salary="$120,000 - $160,000",
                posted_date="2024-01-15"
            ),
            JobResult(
                id="mock_2",
                title="Data Scientist",
                company="DataFlow Analytics",
                location="New York, NY",
                description="Join our data science team to build machine learning models and analyze large datasets. PhD in Statistics or related field preferred.",
                salary="$110,000 - $140,000",
                posted_date="2024-01-14"
            ),
            JobResult(
                id="mock_3",
                title="Frontend Developer",
                company="WebDesign Studio",
                location="Austin, TX",
                description="Create beautiful and responsive web applications using React and modern CSS frameworks.",
                salary="$80,000 - $100,000",
                posted_date="2024-01-13"
            )
        ]

        # Simple keyword filtering for mock data
        if query.keyword:
            keyword_lower = query.keyword.lower()
            filtered_jobs = []
            for job in mock_jobs:
                if (keyword_lower in job.title.lower() or
                    keyword_lower in job.description.lower() or
                    keyword_lower in job.company.lower()):
                    filtered_jobs.append(job)
            mock_jobs = filtered_jobs

        # Apply pagination
        start_idx = (query.page - 1) * query.size
        end_idx = start_idx + query.size
        paginated_jobs = mock_jobs[start_idx:end_idx]

        return SearchResults(
            jobs=paginated_jobs,
            total_count=len(mock_jobs),
            page=query.page,
            size=query.size,
            query=query.keyword or ""
        )
