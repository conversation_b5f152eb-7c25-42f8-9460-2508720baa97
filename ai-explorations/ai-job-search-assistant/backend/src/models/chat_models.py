"""
Pydantic models for chat functionality
"""
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime

class ChatMessage(BaseModel):
    content: str
    user_id: str
    session_id: Optional[str] = "default"
    timestamp: Optional[datetime] = None
    message_type: Optional[str] = "user"

class ChatResponse(BaseModel):
    content: str
    message_type: str = "assistant"
    metadata: Optional[Dict[str, Any]] = {}
    timestamp: Optional[datetime] = None

class JobSearchQuery(BaseModel):
    keyword: Optional[str] = None
    location: Optional[str] = None
    radius: Optional[int] = 50
    page: Optional[int] = 1
    size: Optional[int] = 10
    filters: Optional[Dict[str, Any]] = {}

class JobResult(BaseModel):
    id: str
    title: str
    company: str
    location: str
    description: str
    salary: Optional[str] = None
    url: Optional[str] = None
    posted_date: Optional[str] = None

class SearchResults(BaseModel):
    jobs: List[JobResult]
    total_count: int
    page: int
    size: int
    query: str

class ConversationState(BaseModel):
    user_id: str
    session_id: str
    messages: List[ChatMessage] = []
    last_search_query: Optional[JobSearchQuery] = None
    last_search_results: Optional[SearchResults] = None
    context: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime
