"""
Basic tests for the AI Chatbot service
"""
import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock
from fastapi.testclient import TestClient

from main import app
from services.opensearch_service import OpenSearchService
from models.chat_models import Job<PERSON>earch<PERSON><PERSON>y, JobResult, SearchResults

# Test client
client = TestClient(app)

def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "AI Chatbot Service is running"}

def test_health_endpoint():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data

def test_chat_endpoint():
    """Test the chat REST endpoint"""
    message_data = {
        "content": "Hello, I'm looking for software engineer jobs",
        "user_id": "test_user",
        "session_id": "test_session"
    }
    
    # Note: This will fail without proper OpenAI API key and OpenSearch setup
    # In a real test environment, you'd mock these dependencies
    response = client.post("/api/chat", json=message_data)
    
    # For now, we expect it to fail gracefully
    assert response.status_code in [200, 500]  # Either success or expected failure

class TestOpenSearchService:
    """Test OpenSearch service functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.service = OpenSearchService()
    
    def test_build_search_query_with_keyword(self):
        """Test building search query with keyword"""
        query = JobSearchQuery(keyword="python developer")
        search_body = self.service._build_search_query(query)
        
        assert "query" in search_body
        assert "bool" in search_body["query"]
        assert len(search_body["query"]["bool"]["must"]) > 0
        
        # Check if keyword search is included
        multi_match = search_body["query"]["bool"]["must"][0]
        assert multi_match["multi_match"]["query"] == "python developer"
    
    def test_build_search_query_with_location(self):
        """Test building search query with location"""
        query = JobSearchQuery(keyword="engineer", location="San Francisco")
        search_body = self.service._build_search_query(query)
        
        assert len(search_body["query"]["bool"]["filter"]) > 0
        location_filter = search_body["query"]["bool"]["filter"][0]
        assert location_filter["match"]["location"] == "San Francisco"
    
    def test_build_search_query_with_filters(self):
        """Test building search query with custom filters"""
        query = JobSearchQuery(
            keyword="developer",
            filters={"employment_type": "Full-time", "remote": True}
        )
        search_body = self.service._build_search_query(query)
        
        # Should have 2 filters
        assert len(search_body["query"]["bool"]["filter"]) == 2

class TestJobModels:
    """Test Pydantic models"""
    
    def test_job_search_query_creation(self):
        """Test JobSearchQuery model"""
        query = JobSearchQuery(
            keyword="python",
            location="New York",
            radius=25,
            page=1,
            size=20
        )
        
        assert query.keyword == "python"
        assert query.location == "New York"
        assert query.radius == 25
        assert query.page == 1
        assert query.size == 20
    
    def test_job_result_creation(self):
        """Test JobResult model"""
        job = JobResult(
            id="job_1",
            title="Software Engineer",
            company="TechCorp",
            location="San Francisco, CA",
            description="Great job opportunity",
            salary="$100k - $120k"
        )
        
        assert job.id == "job_1"
        assert job.title == "Software Engineer"
        assert job.company == "TechCorp"
    
    def test_search_results_creation(self):
        """Test SearchResults model"""
        jobs = [
            JobResult(
                id="job_1",
                title="Engineer",
                company="Company A",
                location="Location A",
                description="Description A"
            )
        ]
        
        results = SearchResults(
            jobs=jobs,
            total_count=1,
            page=1,
            size=10,
            query="engineer"
        )
        
        assert len(results.jobs) == 1
        assert results.total_count == 1
        assert results.query == "engineer"

# Mock tests for agent functionality
class TestChatAgent:
    """Test chat agent functionality with mocks"""
    
    @pytest.fixture
    def mock_opensearch_service(self):
        """Mock OpenSearch service"""
        service = Mock(spec=OpenSearchService)
        service.search_jobs = AsyncMock(return_value=SearchResults(
            jobs=[
                JobResult(
                    id="test_job",
                    title="Test Engineer",
                    company="Test Company",
                    location="Test Location",
                    description="Test description"
                )
            ],
            total_count=1,
            page=1,
            size=10,
            query="test"
        ))
        return service
    
    def test_intent_classification(self, mock_opensearch_service):
        """Test intent classification logic"""
        # This would test the agent's ability to classify user intents
        # For now, we'll just test that job-related keywords are detected
        
        job_messages = [
            "I'm looking for software engineer jobs",
            "Find me Python developer positions",
            "Show me remote work opportunities"
        ]
        
        general_messages = [
            "Hello, how are you?",
            "What's the weather like?",
            "Tell me a joke"
        ]
        
        # Simple keyword-based classification test
        job_keywords = ["job", "work", "position", "career", "employment"]
        
        for message in job_messages:
            has_job_keyword = any(keyword in message.lower() for keyword in job_keywords)
            assert has_job_keyword, f"Message '{message}' should be classified as job-related"
        
        for message in general_messages:
            has_job_keyword = any(keyword in message.lower() for keyword in job_keywords)
            assert not has_job_keyword, f"Message '{message}' should not be classified as job-related"

if __name__ == "__main__":
    pytest.main([__file__])
