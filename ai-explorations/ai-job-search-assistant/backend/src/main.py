"""
AI Chatbot Service with LangGraph-based agentic flow
"""
import os
import uvicorn
from fastapi import <PERSON>AP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
from typing import List, Dict, Any
import json
import asyncio
from datetime import datetime

from agents.chat_agent import Chat<PERSON><PERSON>
from models.chat_models import ChatMessage, ChatResponse
from services.opensearch_service import OpenSearchService

app = FastAPI(title="AI Chatbot Service", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
opensearch_service = OpenSearchService()
chat_agent = ChatAgent(opensearch_service)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self, max_connections: int = 100):
        self.active_connections: List[WebSocket] = []
        self.user_sessions: Dict[str, Dict] = {}
        self.max_connections = max_connections

    async def connect(self, websocket: WebSocket, user_id: str):
        # Check connection limit
        if len(self.active_connections) >= self.max_connections:
            await websocket.close(code=1013, reason="Server overloaded")
            print(f"Connection rejected for {user_id}: max connections reached")
            return False

        await websocket.accept()
        self.active_connections.append(websocket)
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                "websocket": websocket,
                "chat_history": [],
                "created_at": datetime.now()
            }
        return True

    def disconnect(self, websocket: WebSocket, user_id: str):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        print(f"Disconnected user {user_id}. Active connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def send_message_to_user(self, message: str, user_id: str):
        if user_id in self.user_sessions:
            websocket = self.user_sessions[user_id]["websocket"]
            await websocket.send_text(message)

    def clear_all_connections(self):
        """Clear all connections - useful for debugging"""
        self.active_connections.clear()
        self.user_sessions.clear()
        print("Cleared all connections")

manager = ConnectionManager()

@app.get("/")
async def root():
    return {"message": "AI Chatbot Service is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/debug")
async def debug_info():
    """Debug endpoint to check service status"""
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "opensearch_connected": opensearch_service.client is not None,
        "openai_configured": chat_agent.llm is not None,
        "active_connections": len(manager.active_connections),
        "user_sessions": len(manager.user_sessions)
    }

@app.post("/debug/clear-connections")
async def clear_connections():
    """Clear all WebSocket connections for debugging"""
    manager.clear_all_connections()
    return {"message": "All connections cleared", "timestamp": datetime.now().isoformat()}

@app.post("/api/chat")
async def chat_endpoint(message: ChatMessage):
    """REST endpoint for chat messages"""
    try:
        response = await chat_agent.process_message(
            message.content,
            message.user_id,
            message.session_id
        )
        return ChatResponse(
            content=response["content"],
            message_type=response.get("type", "text"),
            metadata=response.get("metadata", {})
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time chat"""
    print(f"WebSocket connection attempt for user: {user_id}")

    try:
        connected = await manager.connect(websocket, user_id)
        if not connected:
            return
        print(f"WebSocket connected for user: {user_id}")

        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                print(f"Received message from {user_id}: {data}")

                message_data = json.loads(data)

                # Process message through agent
                print(f"Processing message through agent...")
                response = await chat_agent.process_message(
                    message_data["content"],
                    user_id,
                    message_data.get("session_id", "default")
                )
                print(f"Agent response: {response}")

                # Send response back to client
                response_json = json.dumps({
                    "content": response["content"],
                    "type": response.get("type", "text"),
                    "metadata": response.get("metadata", {}),
                    "timestamp": datetime.now().isoformat()
                })

                await manager.send_personal_message(response_json, websocket)
                print(f"Sent response to {user_id}")

            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                await manager.send_personal_message(
                    json.dumps({
                        "content": "Invalid message format. Please try again.",
                        "type": "error",
                        "timestamp": datetime.now().isoformat()
                    }),
                    websocket
                )
            except Exception as e:
                print(f"Error processing message: {e}")
                await manager.send_personal_message(
                    json.dumps({
                        "content": f"Error processing message: {str(e)}",
                        "type": "error",
                        "timestamp": datetime.now().isoformat()
                    }),
                    websocket
                )

    except WebSocketDisconnect:
        print(f"WebSocket disconnected for user: {user_id}")
        manager.disconnect(websocket, user_id)
    except Exception as e:
        print(f"WebSocket error for user {user_id}: {e}")
        try:
            await manager.send_personal_message(
                json.dumps({
                    "content": f"Connection error: {str(e)}",
                    "type": "error",
                    "timestamp": datetime.now().isoformat()
                }),
                websocket
            )
        except:
            pass
        manager.disconnect(websocket, user_id)

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
