#!/usr/bin/env python3
"""
Simple WebSocket test client to verify the chatbot is working
"""
import asyncio
import websockets
import json
import sys

async def test_websocket():
    uri = "ws://localhost:8000/ws/test_user"
    
    try:
        print("Connecting to WebSocket...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected successfully!")
            
            # Test messages
            test_messages = [
                "Hello!",
                "Find software engineer jobs",
                "Show me data science positions",
                "What skills should I learn for web development?"
            ]
            
            for message in test_messages:
                print(f"\n📤 Sending: {message}")
                
                # Send message
                await websocket.send(json.dumps({
                    "content": message,
                    "session_id": "test_session"
                }))
                
                # Wait for response
                response = await websocket.recv()
                response_data = json.loads(response)
                
                print(f"📥 Received: {response_data['content'][:100]}...")
                print(f"   Type: {response_data.get('type', 'unknown')}")
                print(f"   Metadata: {response_data.get('metadata', {})}")
                
                # Wait a bit between messages
                await asyncio.sleep(1)
            
            print("\n✅ All tests completed successfully!")
            
    except websockets.exceptions.ConnectionClosed:
        print("❌ WebSocket connection closed unexpectedly")
        return False
    except ConnectionRefusedError:
        print("❌ Could not connect to WebSocket server. Is the backend running?")
        print("   Try: docker-compose -f docker-compose.chatbot.yml up -d")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🤖 Testing AI Chatbot WebSocket Connection")
    print("=" * 50)
    
    success = asyncio.run(test_websocket())
    
    if success:
        print("\n🎉 WebSocket test completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 WebSocket test failed!")
        sys.exit(1)
