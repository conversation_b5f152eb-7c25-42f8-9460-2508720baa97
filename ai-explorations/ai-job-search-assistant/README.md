# AI Job Search Chatbot

A modern, real-time AI-powered chatbot for job searching with OpenSearch integration and intelligent conversation capabilities.

## 🚀 Features

### 🤖 AI-Powered Conversations
- **Natural Language Processing** with OpenAI GPT models
- **Intent Recognition** for job search vs. general conversation
- **Context-Aware Responses** that understand user needs
- **Real-time WebSocket Communication** for instant responses

### 🔍 Advanced Job Search
- **Semantic Search** through job listings using OpenSearch
- **Fuzzy Matching** for flexible job title and description searches
- **Location-Based Filtering** for geographic job searches
- **Company and Skill-Based Search** with intelligent matching
- **Salary Information** display and filtering
- **Posted Date Sorting** for fresh job listings

### 🏗️ Modern Architecture
- **React Frontend** with TypeScript and Tailwind CSS
- **FastAPI Backend** with async WebSocket support
- **OpenSearch Integration** for powerful job data indexing
- **Docker Containerization** for easy deployment
- **Microservices Architecture** with clear separation of concerns

### 🔧 Flexible Deployment Options
- **Local OpenSearch** with seeded job data
- **External OpenSearch** cluster support (AWS, self-hosted)
- **Mock Data Fallback** for development and testing
- **Environment-Based Configuration** for different setups

## 📋 Prerequisites

- **Docker & Docker Compose** (latest version)
- **OpenAI API Key** for AI conversations
- **Node.js 18+** (for local development)
- **Python 3.9+** (for local development)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ai-job-search-chatbot
```

### 2. Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Start with Local OpenSearch (Recommended)
```bash
# Start all services with local OpenSearch and job data
docker-compose -f docker-compose.chatbot.yml up -d

# Wait for OpenSearch to be ready (30 seconds)
sleep 30

# Seed the database with sample job data
docker-compose -f docker-compose.chatbot.yml exec chatbot-backend python src/scripts/seed_opensearch.py
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **OpenSearch Dashboards**: http://localhost:5601
- **Health Check**: http://localhost:8000/debug

## 🎯 Usage Examples

### Job Search Queries
```
"Find software engineer jobs"
"Show me data science positions in San Francisco"
"DevOps engineer jobs with salary over 100k"
"Frontend developer positions at tech companies"
"Remote Python developer jobs"
```

### General Conversation
```
"Hello, how can you help me?"
"What skills should I learn for data science?"
"How do I prepare for a software engineering interview?"
"Tell me about the current job market"
```

## 🔧 Configuration Options

### Local Setup (Default)
Uses local OpenSearch with seeded job data:
```bash
docker-compose -f docker-compose.chatbot.yml up -d
```

### Lite Setup (Mock Data Only)
For development without OpenSearch:
```bash
docker-compose -f docker-compose.chatbot-lite.yml up -d
```

### External OpenSearch Setup
For production with external OpenSearch cluster:
```bash
# Configure external OpenSearch
cp .env.external-opensearch.example .env
# Edit .env with your external OpenSearch details

# Test connection
python setup_external_opensearch.py

# Start with external configuration
docker-compose -f docker-compose.chatbot-external.yml up -d
```

## 📊 Sample Job Data

The local setup includes 10 sample job listings:

- **Senior Software Engineer** at TechCorp Inc. (San Francisco, CA)
- **Data Scientist** at DataFlow Analytics (New York, NY)
- **Frontend Developer** at WebDesign Studio (Austin, TX)
- **DevOps Engineer** at CloudFirst Solutions (Seattle, WA)
- **Product Manager** at InnovateTech (Boston, MA)
- **UX Designer** at DesignHub (Los Angeles, CA)
- **Backend Developer** at ServerSide Systems (Chicago, IL)
- **Marketing Specialist** at GrowthMarketing Co. (Miami, FL)
- **Machine Learning Engineer** at AI Innovations (Palo Alto, CA)
- **Sales Representative** at SalesForce Pro (Denver, CO)

## 🏗️ Architecture

### Frontend (React + TypeScript)
```
frontend/
├── src/
│   ├── app/
│   │   ├── page.tsx          # Main chat interface
│   │   └── layout.tsx        # App layout
│   ├── components/           # Reusable UI components
│   └── types/               # TypeScript definitions
```

### Backend (FastAPI + Python)
```
backend/
├── src/
│   ├── main.py              # FastAPI application
│   ├── agents/
│   │   └── chat_agent.py    # AI conversation logic
│   ├── services/
│   │   └── opensearch_service.py  # Job search service
│   ├── models/              # Data models
│   └── scripts/
│       └── seed_opensearch.py     # Database seeding
```

### Infrastructure
- **OpenSearch**: Job data indexing and search
- **WebSocket**: Real-time communication
- **Docker**: Containerized deployment
- **Environment Variables**: Configuration management

## 🔍 API Endpoints

### WebSocket
- **`/ws/{user_id}`**: Real-time chat communication

### REST API
- **`GET /health`**: Health check
- **`GET /debug`**: System status and diagnostics
- **`POST /api/chat`**: Send chat message (alternative to WebSocket)
- **`POST /debug/clear-connections`**: Clear WebSocket connections (debug)

## 🛠️ Development

### Local Development Setup
```bash
# Backend development
cd backend
pip install -r requirements.txt
python src/main.py

# Frontend development
cd frontend
npm install
npm run dev
```

### Running Tests
```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

### Adding New Job Data
```bash
# Connect to OpenSearch container
docker-compose -f docker-compose.chatbot.yml exec opensearch bash

# Or use the seeding script
docker-compose -f docker-compose.chatbot.yml exec chatbot-backend python src/scripts/seed_opensearch.py
```

## 🔧 Troubleshooting

### Common Issues

#### WebSocket Connection Failures
```bash
# Check backend logs
docker-compose -f docker-compose.chatbot.yml logs chatbot-backend

# Clear connections
curl -X POST http://localhost:8000/debug/clear-connections
```

#### OpenSearch Connection Issues
```bash
# Check OpenSearch status
curl http://localhost:9200

# Restart backend to reconnect
docker-compose -f docker-compose.chatbot.yml restart chatbot-backend
```

#### Frontend Build Issues
```bash
# Rebuild frontend
docker-compose -f docker-compose.chatbot.yml build chatbot-frontend
docker-compose -f docker-compose.chatbot.yml restart chatbot-frontend
```

### Debug Information
```bash
# System status
curl http://localhost:8000/debug

# Expected response:
{
  "status": "running",
  "opensearch_connected": true,
  "openai_configured": true,
  "active_connections": 0
}
```

## 🌐 External OpenSearch Setup

### AWS OpenSearch Service
```bash
# .env configuration
OPENSEARCH_URL=https://search-your-domain.us-east-1.es.amazonaws.com
OPENSEARCH_USE_AWS_AUTH=true
AWS_REGION=us-east-1
OPENSEARCH_INDEX=jobs
```

### Self-Hosted OpenSearch
```bash
# .env configuration
OPENSEARCH_URL=https://opensearch.yourcompany.com:9200
OPENSEARCH_USERNAME=your_username
OPENSEARCH_PASSWORD=your_password
OPENSEARCH_INDEX=jobs
```

### Job Index Schema
Expected OpenSearch document structure:
```json
{
  "title": "Senior Software Engineer",
  "company": "TechCorp Inc.",
  "location": "San Francisco, CA",
  "description": "Job description...",
  "salary": "$120,000 - $160,000",
  "url": "https://company.com/jobs/123",
  "posted_date": "2024-01-15",
  "skills": ["Python", "JavaScript", "AWS"],
  "employment_type": "Full-time",
  "remote": true
}
```

## 📝 Environment Variables

### Required
- `OPENAI_API_KEY`: OpenAI API key for AI conversations

### OpenSearch Configuration
- `OPENSEARCH_URL`: Full OpenSearch cluster URL
- `OPENSEARCH_HOST`: OpenSearch hostname (alternative to URL)
- `OPENSEARCH_PORT`: OpenSearch port (default: 9200)
- `OPENSEARCH_USERNAME`: Basic auth username
- `OPENSEARCH_PASSWORD`: Basic auth password
- `OPENSEARCH_USE_SSL`: Enable SSL (default: true for external)
- `OPENSEARCH_INDEX`: Job index name (default: jobs)

### AWS OpenSearch
- `OPENSEARCH_USE_AWS_AUTH`: Enable AWS IAM auth
- `AWS_REGION`: AWS region
- `AWS_ACCESS_KEY_ID`: AWS access key
- `AWS_SECRET_ACCESS_KEY`: AWS secret key

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript/Python best practices
- Add tests for new features
- Update documentation for API changes
- Use conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for GPT models and AI capabilities
- **OpenSearch** for powerful search and indexing
- **FastAPI** for modern Python web framework
- **React** for frontend user interface
- **Docker** for containerization

## 📞 Support

For issues and questions:
1. Check the [Troubleshooting](#-troubleshooting) section
2. Review [GitHub Issues](../../issues)
3. Create a new issue with detailed information

---

**Built with ❤️ for modern job searching**
