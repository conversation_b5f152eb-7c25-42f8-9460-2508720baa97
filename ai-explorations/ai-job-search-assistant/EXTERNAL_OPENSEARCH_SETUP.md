# External OpenSearch Setup for AI Chatbot

This guide explains how to configure the AI Chatbot to connect to an external OpenSearch cluster with existing job data.

## 🎯 Overview

The chatbot can connect to:
- **AWS OpenSearch Service** (managed)
- **Self-hosted OpenSearch clusters**
- **OpenSearch Cloud** instances
- **Local OpenSearch** (development)

## 🚀 Quick Setup

### 1. Configure Environment

Copy the example environment file:
```bash
cp .env.external-opensearch.example .env
```

Edit `.env` with your OpenSearch cluster details:
```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here
OPENSEARCH_URL=https://your-opensearch-domain.com

# Authentication (choose one method)
OPENSEARCH_USERNAME=your_username
OPENSEARCH_PASSWORD=your_password

# Index settings
OPENSEARCH_INDEX=jobs
```

### 2. Test Connection

Run the setup script to verify your configuration:
```bash
python setup_external_opensearch.py
```

### 3. Start the Chatbot

```bash
docker-compose -f docker-compose.chatbot-external.yml up -d
```

### 4. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health

## 🔧 Configuration Options

### Authentication Methods

#### Basic Authentication
```bash
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=your_password
```

#### API Key Authentication
```bash
OPENSEARCH_API_KEY=your_api_key_here
```

#### AWS IAM Authentication
```bash
OPENSEARCH_USE_AWS_AUTH=true
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
```

### Connection Settings

```bash
# Full URL (recommended)
OPENSEARCH_URL=https://search-domain.us-east-1.es.amazonaws.com

# Or individual components
OPENSEARCH_HOST=your-host.com
OPENSEARCH_PORT=443
OPENSEARCH_USE_SSL=true

# Security settings
OPENSEARCH_VERIFY_CERTS=true
OPENSEARCH_TIMEOUT=30
```

## 📊 Expected Job Index Schema

The chatbot expects a `jobs` index with documents containing these fields:

```json
{
  "title": "Senior Software Engineer",
  "company": "TechCorp Inc.",
  "location": "San Francisco, CA",
  "description": "Job description text...",
  "salary": "$120,000 - $160,000",
  "url": "https://company.com/jobs/123",
  "posted_date": "2024-01-15",
  "skills": ["Python", "JavaScript", "AWS"],
  "employment_type": "Full-time",
  "remote": true
}
```

### Required Fields
- `title` (string): Job title
- `company` (string): Company name
- `location` (string): Job location
- `description` (string): Job description

### Optional Fields
- `salary` (string): Salary information
- `url` (string): Job posting URL
- `posted_date` (string): Date posted
- `skills` (array): Required skills
- `employment_type` (string): Full-time, Part-time, etc.
- `remote` (boolean): Remote work option

## 🔍 Testing Your Setup

### 1. Check Backend Connection
```bash
curl http://localhost:8000/debug
```

Should return:
```json
{
  "status": "running",
  "opensearch_connected": true,
  "openai_configured": true,
  "active_connections": 0
}
```

### 2. Test Job Search
```bash
curl -X POST http://localhost:8000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content": "Find software engineer jobs", "session_id": "test"}'
```

### 3. Test WebSocket Connection
Open http://localhost:3000 and try:
- "Hello" (general conversation)
- "Find software engineer jobs" (job search)
- "Show me data science positions" (job search)

## 🛠️ Troubleshooting

### Connection Issues

1. **SSL Certificate Errors**
   ```bash
   OPENSEARCH_VERIFY_CERTS=false
   ```

2. **Timeout Issues**
   ```bash
   OPENSEARCH_TIMEOUT=60
   ```

3. **Authentication Failures**
   - Verify credentials are correct
   - Check if IP is whitelisted
   - Ensure proper IAM permissions for AWS

### Index Issues

1. **Index Not Found**
   ```bash
   # Check if index exists
   curl -X GET "https://your-domain.com/jobs"
   ```

2. **No Search Results**
   - Verify index contains data
   - Check field mappings match expected schema
   - Test with simple queries first

### AWS OpenSearch Service

1. **IAM Permissions Required**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "es:ESHttpGet",
           "es:ESHttpPost"
         ],
         "Resource": "arn:aws:es:region:account:domain/domain-name/*"
       }
     ]
   }
   ```

2. **VPC Access**
   - Ensure chatbot can reach OpenSearch endpoint
   - Check security groups and NACLs

## 📝 Example Configurations

### AWS OpenSearch Service
```bash
OPENSEARCH_URL=https://search-jobs.us-east-1.es.amazonaws.com
OPENSEARCH_USE_AWS_AUTH=true
AWS_REGION=us-east-1
OPENSEARCH_INDEX=jobs
```

### Self-hosted with Basic Auth
```bash
OPENSEARCH_URL=https://opensearch.company.com:9200
OPENSEARCH_USERNAME=chatbot_user
OPENSEARCH_PASSWORD=secure_password
OPENSEARCH_INDEX=job_listings
```

### Local Development
```bash
OPENSEARCH_URL=http://localhost:9200
OPENSEARCH_USERNAME=admin
OPENSEARCH_PASSWORD=admin
OPENSEARCH_INDEX=jobs
OPENSEARCH_VERIFY_CERTS=false
```

## 🔄 Fallback Behavior

If OpenSearch connection fails, the chatbot automatically falls back to mock data with sample jobs. This ensures the application remains functional during:
- Network issues
- Authentication problems
- Index unavailability

## 📞 Support

If you encounter issues:
1. Run `python setup_external_opensearch.py` to diagnose problems
2. Check backend logs: `docker-compose -f docker-compose.chatbot-external.yml logs chatbot-backend`
3. Verify OpenSearch cluster health and accessibility
