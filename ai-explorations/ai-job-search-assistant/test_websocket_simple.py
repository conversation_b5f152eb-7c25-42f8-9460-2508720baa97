#!/usr/bin/env python3
"""
Simple WebSocket test using only standard library
"""
import socket
import base64
import hashlib
import json
import sys
import time

def create_websocket_key():
    """Generate a WebSocket key"""
    import random
    import string
    key = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))
    return base64.b64encode(key.encode()).decode()

def test_websocket_handshake():
    """Test WebSocket handshake without external dependencies"""
    try:
        print("🔌 Testing WebSocket handshake...")
        
        # Create socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        # Connect to server
        print("Connecting to localhost:8000...")
        sock.connect(('localhost', 8000))
        
        # Create WebSocket handshake
        key = create_websocket_key()
        user_id = "test_user_simple"
        
        handshake = (
            f"GET /ws/{user_id} HTTP/1.1\r\n"
            "Host: localhost:8000\r\n"
            "Upgrade: websocket\r\n"
            "Connection: Upgrade\r\n"
            f"Sec-WebSocket-Key: {key}\r\n"
            "Sec-WebSocket-Version: 13\r\n"
            "\r\n"
        )
        
        print(f"Sending handshake for user: {user_id}")
        sock.send(handshake.encode())
        
        # Read response
        response = sock.recv(1024).decode()
        print("Handshake response:")
        print(response)
        
        if "101 Switching Protocols" in response:
            print("✅ WebSocket handshake successful!")
            
            # Try to send a simple message (basic WebSocket frame)
            message = json.dumps({
                "content": "Hello from simple test",
                "session_id": "test_session"
            })
            
            # Create a simple WebSocket frame (text frame, not masked for simplicity)
            # This is a very basic implementation
            payload = message.encode('utf-8')
            frame = bytearray()
            frame.append(0x81)  # FIN=1, opcode=1 (text)
            
            if len(payload) < 126:
                frame.append(len(payload))
            else:
                frame.append(126)
                frame.extend(len(payload).to_bytes(2, 'big'))
            
            frame.extend(payload)
            
            print(f"Sending message: {message}")
            sock.send(frame)
            
            # Try to read response (simplified)
            try:
                sock.settimeout(3)
                response = sock.recv(1024)
                print(f"Received response: {len(response)} bytes")
                if response:
                    print("✅ Server responded to WebSocket message")
                else:
                    print("⚠️  No response received")
            except socket.timeout:
                print("⚠️  Timeout waiting for response")
            
        else:
            print("❌ WebSocket handshake failed")
            return False
            
        sock.close()
        return True
        
    except ConnectionRefusedError:
        print("❌ Connection refused. Is the backend running?")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_http_endpoints():
    """Test HTTP endpoints first"""
    import urllib.request
    import urllib.error
    
    endpoints = [
        ("Health Check", "http://localhost:8000/health"),
        ("Debug Info", "http://localhost:8000/debug"),
        ("Root", "http://localhost:8000/")
    ]
    
    print("🌐 Testing HTTP endpoints...")
    all_good = True
    
    for name, url in endpoints:
        try:
            with urllib.request.urlopen(url, timeout=5) as response:
                data = response.read().decode()
                print(f"✅ {name}: {response.status}")
                if "debug" in url.lower():
                    try:
                        debug_info = json.loads(data)
                        print(f"   - OpenSearch connected: {debug_info.get('opensearch_connected', 'unknown')}")
                        print(f"   - OpenAI configured: {debug_info.get('openai_configured', 'unknown')}")
                        print(f"   - Active connections: {debug_info.get('active_connections', 'unknown')}")
                    except:
                        pass
        except urllib.error.URLError as e:
            print(f"❌ {name}: {e}")
            all_good = False
        except Exception as e:
            print(f"❌ {name}: {e}")
            all_good = False
    
    return all_good

if __name__ == "__main__":
    print("🤖 Simple WebSocket Test (No External Dependencies)")
    print("=" * 60)
    
    # Test HTTP first
    http_ok = test_http_endpoints()
    
    print()
    
    if http_ok:
        # Test WebSocket
        ws_ok = test_websocket_handshake()
        
        if ws_ok:
            print("\n🎉 WebSocket test completed successfully!")
            print("💡 Try opening test_websocket.html in your browser for interactive testing")
        else:
            print("\n💥 WebSocket test failed!")
            print("💡 Check backend logs: docker-compose -f docker-compose.chatbot.yml logs chatbot-backend")
    else:
        print("\n💥 HTTP endpoints failed - backend may not be running")
        print("💡 Try: docker-compose -f docker-compose.chatbot.yml up -d")
    
    print("\n📋 Next steps:")
    print("1. Open http://localhost:3000 in your browser")
    print("2. Open browser dev tools (F12) and check Console tab")
    print("3. Try sending a message and watch for errors")
    print("4. Check backend logs for detailed error messages")
