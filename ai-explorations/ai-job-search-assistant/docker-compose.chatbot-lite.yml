version: '3.8'

services:
  # AI Chatbot Backend (without OpenSearch)
  chatbot-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chatbot-backend-lite
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OP<PERSON><PERSON>ARCH_HOST=localhost
      - OPENSEARCH_PORT=9200
      - OPENSEARCH_USERNAME=admin
      - OPENSEARCH_PASSWORD=admin
      - OPENSEARCH_USE_SSL=false
      - OPENSEARCH_INDEX=jobs
      - PORT=8000
      - DEBUG=true
    volumes:
      - ./backend/src:/app/src
    networks:
      - chatbot-network
    restart: unless-stopped

  # AI Chatbot Frontend
  chatbot-frontend:
    image: node:18-alpine
    container_name: chatbot-frontend-lite
    working_dir: /app
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_CHATBOT_API_URL=http://localhost:8000
      - NEXT_PUBLIC_CHATBOT_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
    command: sh -c "npm install && npm run dev"
    networks:
      - chatbot-network
    depends_on:
      - chatbot-backend
    restart: unless-stopped

networks:
  chatbot-network:
    driver: bridge
