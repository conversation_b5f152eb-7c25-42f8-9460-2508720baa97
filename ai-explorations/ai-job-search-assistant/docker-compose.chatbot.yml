version: '3.8'

services:
  # OpenSearch for job search
  opensearch:
    image: opensearchproject/opensearch:2.11.0
    container_name: opensearch-chatbot
    environment:
      - cluster.name=opensearch-cluster
      - node.name=opensearch-node1
      - discovery.type=single-node
      - bootstrap.memory_lock=false
      - "OPENSEARCH_JAVA_OPTS=-Xms256m -Xmx256m"
      - "DISABLE_INSTALL_DEMO_CONFIG=true"
      - "DISABLE_SECURITY_PLUGIN=true"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - opensearch-data:/usr/share/opensearch/data
    ports:
      - "9200:9200"
      - "9600:9600"
    networks:
      - chatbot-network

  # OpenSearch Dashboards (optional, for debugging)
  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2.11.0
    container_name: opensearch-dashboards-chatbot
    ports:
      - "5601:5601"
    expose:
      - "5601"
    environment:
      - 'OPENSEARCH_HOSTS=["http://opensearch:9200"]'
      - "DISABLE_SECURITY_DASHBOARDS_PLUGIN=true"
    networks:
      - chatbot-network
    depends_on:
      - opensearch

  # AI Chatbot Backend
  chatbot-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chatbot-backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENSEARCH_HOST=opensearch
      - OPENSEARCH_PORT=9200
      - OPENSEARCH_USERNAME=admin
      - OPENSEARCH_PASSWORD=admin
      - OPENSEARCH_USE_SSL=false
      - OPENSEARCH_INDEX=jobs
      - PORT=8000
      - DEBUG=true
    volumes:
      - ./backend/src:/app/src
    networks:
      - chatbot-network
    depends_on:
      - opensearch
    restart: unless-stopped

  # AI Chatbot Frontend
  chatbot-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: chatbot-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_CHATBOT_API_URL=http://localhost:8000
      - NEXT_PUBLIC_CHATBOT_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    networks:
      - chatbot-network
    depends_on:
      - chatbot-backend
    restart: unless-stopped

volumes:
  opensearch-data:

networks:
  chatbot-network:
    driver: bridge
