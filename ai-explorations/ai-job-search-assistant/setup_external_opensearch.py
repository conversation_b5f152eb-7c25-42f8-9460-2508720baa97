#!/usr/bin/env python3
"""
Setup script for external OpenSearch configuration
"""
import os
import sys
import json
from urllib.parse import urlparse

def test_opensearch_connection():
    """Test connection to external OpenSearch cluster"""
    try:
        from opensearchpy import OpenSearch
        import requests
    except ImportError:
        print("❌ Required packages not installed. Run: pip install opensearch-py requests")
        return False

    # Load environment variables
    opensearch_url = os.getenv("OPENSEARCH_URL")
    if not opensearch_url:
        print("❌ OPENSEARCH_URL not set in environment")
        return False

    # Parse URL
    parsed = urlparse(opensearch_url)
    host = parsed.hostname
    port = parsed.port or (443 if parsed.scheme == 'https' else 9200)
    use_ssl = parsed.scheme == 'https'

    print(f"🔍 Testing connection to {host}:{port} (SSL: {use_ssl})")

    # Authentication
    username = os.getenv("OPENSEARCH_USERNAME")
    password = os.getenv("OPENSEARCH_PASSWORD")
    api_key = os.getenv("OPENSEARCH_API_KEY")
    use_aws_auth = os.getenv("OPENSEARCH_USE_AWS_AUTH", "false").lower() == "true"

    try:
        connection_params = {
            "hosts": [{"host": host, "port": port}],
            "use_ssl": use_ssl,
            "verify_certs": os.getenv("OPENSEARCH_VERIFY_CERTS", "true").lower() == "true",
            "ssl_assert_hostname": False,
            "ssl_show_warn": False,
            "timeout": int(os.getenv("OPENSEARCH_TIMEOUT", "30"))
        }

        # Add authentication
        if use_aws_auth:
            try:
                import boto3
                from requests_aws4auth import AWS4Auth
                
                credentials = boto3.Session().get_credentials()
                awsauth = AWS4Auth(
                    credentials.access_key,
                    credentials.secret_key,
                    os.getenv("AWS_REGION", "us-east-1"),
                    'es',
                    session_token=credentials.token
                )
                connection_params["http_auth"] = awsauth
                print("🔐 Using AWS IAM authentication")
            except ImportError:
                print("❌ boto3 and requests-aws4auth required for AWS authentication")
                return False
        elif api_key:
            connection_params["api_key"] = api_key
            print("🔐 Using API key authentication")
        elif username and password:
            connection_params["http_auth"] = (username, password)
            print("🔐 Using basic authentication")
        else:
            print("⚠️  No authentication configured")

        # Create client and test connection
        client = OpenSearch(**connection_params)
        info = client.info()
        
        print(f"✅ Connected to OpenSearch {info['version']['number']}")
        print(f"   Cluster: {info['cluster_name']}")
        
        # Test index access
        index_name = os.getenv("OPENSEARCH_INDEX", "jobs")
        try:
            index_info = client.indices.get(index=index_name)
            print(f"✅ Index '{index_name}' found")
            
            # Get document count
            count_response = client.count(index=index_name)
            doc_count = count_response['count']
            print(f"📊 Index contains {doc_count} documents")
            
            # Test search
            search_response = client.search(
                index=index_name,
                body={"query": {"match_all": {}}, "size": 1}
            )
            if search_response['hits']['total']['value'] > 0:
                sample_doc = search_response['hits']['hits'][0]['_source']
                print(f"📄 Sample document fields: {list(sample_doc.keys())}")
            
            return True
            
        except Exception as e:
            print(f"❌ Cannot access index '{index_name}': {e}")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def check_environment():
    """Check if environment is properly configured"""
    print("🔍 Checking environment configuration...")
    
    required_vars = ["OPENAI_API_KEY", "OPENSEARCH_URL"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("💡 Copy .env.external-opensearch.example to .env and configure it")
        return False
    
    print("✅ Required environment variables found")
    return True

def main():
    """Main setup function"""
    print("🚀 External OpenSearch Setup for AI Chatbot")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("📝 No .env file found")
        if os.path.exists(".env.external-opensearch.example"):
            print("💡 Copy .env.external-opensearch.example to .env and configure it")
        else:
            print("💡 Create a .env file with your OpenSearch configuration")
        return
    
    # Load .env file
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Loaded .env file")
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment")
    
    # Check environment
    if not check_environment():
        return
    
    # Test connection
    print("\n🔗 Testing OpenSearch connection...")
    if test_opensearch_connection():
        print("\n🎉 External OpenSearch setup successful!")
        print("\n🚀 Start the chatbot with:")
        print("   docker-compose -f docker-compose.chatbot-external.yml up -d")
    else:
        print("\n❌ OpenSearch connection failed")
        print("💡 Check your configuration in .env file")

if __name__ == "__main__":
    main()
