<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input, button {
            padding: 10px;
            margin: 5px;
        }
        input {
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>🤖 AI Chatbot WebSocket Test</h1>
    
    <div id="status" class="status disconnected">
        Disconnected
    </div>
    
    <div>
        <input type="text" id="messageInput" placeholder="Type a message..." />
        <button onclick="sendMessage()">Send</button>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <h3>Connection Log:</h3>
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        const userId = 'test-user-' + Math.random().toString(36).substr(2, 9);
        const wsUrl = 'ws://localhost:8000/ws/' + userId;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
            }
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected');
                return;
            }
            
            log(`Connecting to: ${wsUrl}`);
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                log('✅ WebSocket connected');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                log(`📥 Received: ${event.data}`);
                try {
                    const data = JSON.parse(event.data);
                    log(`📋 Parsed content: ${data.content}`);
                } catch (e) {
                    log(`❌ Failed to parse JSON: ${e.message}`);
                }
            };
            
            ws.onclose = function(event) {
                log(`🔌 WebSocket closed: Code=${event.code}, Reason=${event.reason}`);
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                log(`❌ WebSocket error: ${error}`);
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close(1000, 'Manual disconnect');
                ws = null;
            }
        }
        
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                log('❌ Please enter a message');
                return;
            }
            
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket not connected');
                return;
            }
            
            const payload = {
                content: message,
                session_id: 'test_session',
                timestamp: new Date().toISOString()
            };
            
            log(`📤 Sending: ${JSON.stringify(payload)}`);
            ws.send(JSON.stringify(payload));
            input.value = '';
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Handle Enter key in input
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Auto-connect on page load
        window.onload = function() {
            log('Page loaded. Click Connect to start testing.');
        };
    </script>
</body>
</html>
