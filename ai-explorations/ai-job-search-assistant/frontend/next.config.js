/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  output: 'standalone',
  experimental: {
    appDir: true,
  },
  env: {
    NEXT_PUBLIC_CHATBOT_API_URL: process.env.NEXT_PUBLIC_CHATBOT_API_URL || 'http://localhost:8000',
    NEXT_PUBLIC_CHATBOT_WS_URL: process.env.NEXT_PUBLIC_CHATBOT_WS_URL || 'ws://localhost:8000',
  },
}

module.exports = nextConfig
