'use client'

import { useState, useEffect, useRef } from 'react'
import { Send, Bot, User, Loader2 } from 'lucide-react'
import { clsx } from 'clsx'

interface Message {
  id: string
  content: string
  type: 'user' | 'assistant'
  timestamp: Date
  metadata?: any
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isConnected, setIsConnected] = useState(false)
  const [ws, setWs] = useState<WebSocket | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  const [userId] = useState(() => 'user-' + Math.random().toString(36).substr(2, 9))
  const [sessionId] = useState(() => 'session-' + Date.now())

  useEffect(() => {
    let websocket: WebSocket | null = null
    let reconnectTimeout: NodeJS.Timeout | null = null
    let reconnectAttempts = 0
    let isComponentMounted = true
    const maxReconnectAttempts = 3

    const connectWebSocket = () => {
      // Don't connect if component is unmounted
      if (!isComponentMounted) return

      // Close existing connection if any
      if (websocket && websocket.readyState !== WebSocket.CLOSED) {
        websocket.close()
      }

      // Build WebSocket URL
      const wsUrl = `${process.env.NEXT_PUBLIC_CHATBOT_WS_URL || 'ws://localhost:8000'}/ws/${userId}`
      console.log('Connecting to WebSocket:', wsUrl)

      try {
        websocket = new WebSocket(wsUrl)

        websocket.onopen = () => {
          if (!isComponentMounted) {
            websocket?.close()
            return
          }
          console.log('WebSocket connected')
          setIsConnected(true)
          setWs(websocket)
          reconnectAttempts = 0 // Reset reconnect attempts on successful connection

          // Add welcome message only once
          setMessages(prev => {
            if (prev.length === 0) {
              return [{
                id: 'welcome',
                content: "Hi! I'm your AI job search assistant. I can help you find jobs, provide career advice, and answer questions about employment. What can I help you with today?",
                type: 'assistant',
                timestamp: new Date()
              }]
            }
            return prev
          })
        }

        websocket.onmessage = (event) => {
          if (!isComponentMounted) return

          console.log('WebSocket message received:', event.data)
          try {
            const data = JSON.parse(event.data)
            const assistantMessage: Message = {
              id: Date.now().toString(),
              content: data.content,
              type: 'assistant',
              timestamp: new Date(data.timestamp),
              metadata: data.metadata
            }
            setMessages(prev => [...prev, assistantMessage])
            setIsLoading(false)
          } catch (error) {
            console.error('Error parsing WebSocket message:', error)
            setIsLoading(false)
          }
        }

        websocket.onclose = (event) => {
          if (!isComponentMounted) return

          console.log('WebSocket closed:', event.code, event.reason)
          setIsConnected(false)
          setWs(null)
          setIsLoading(false)

          // Only attempt to reconnect for unexpected closures and limit reconnection attempts
          if (event.code !== 1000 && event.code !== 1001 && reconnectAttempts < maxReconnectAttempts && isComponentMounted) {
            reconnectAttempts++
            console.log(`Attempting to reconnect in 3 seconds... (attempt ${reconnectAttempts}/${maxReconnectAttempts})`)
            reconnectTimeout = setTimeout(() => {
              if (isComponentMounted) {
                connectWebSocket()
              }
            }, 3000)
          } else if (reconnectAttempts >= maxReconnectAttempts) {
            console.log('Max reconnection attempts reached. Please refresh the page.')
          }
        }

        websocket.onerror = (error) => {
          if (!isComponentMounted) return

          console.error('WebSocket error:', error)
          setIsConnected(false)
          setIsLoading(false)
        }
      } catch (error) {
        console.error('Error creating WebSocket:', error)
        setIsConnected(false)
        setIsLoading(false)
      }
    }

    // Delay initial connection to avoid rapid mount/unmount issues
    const initialTimeout = setTimeout(() => {
      if (isComponentMounted) {
        connectWebSocket()
      }
    }, 100)

    return () => {
      isComponentMounted = false

      if (initialTimeout) {
        clearTimeout(initialTimeout)
      }
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout)
      }
      if (websocket && websocket.readyState !== WebSocket.CLOSED) {
        websocket.close(1000, 'Component unmounting')
      }
    }
  }, [userId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const sendMessage = async () => {
    if (!inputValue.trim() || !ws || !isConnected) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      type: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    // Send message via WebSocket
    ws.send(JSON.stringify({
      content: inputValue,
      session_id: sessionId,
      timestamp: new Date().toISOString()
    }))

    setInputValue('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatMessageContent = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n/g, '<br>')
  }

  return (
    <div className="flex flex-col h-screen max-w-4xl mx-auto bg-white shadow-lg">
      {/* Header */}
      <div className="bg-primary-600 text-white p-4 flex items-center space-x-3">
        <Bot className="w-8 h-8" />
        <div>
          <h1 className="text-xl font-semibold">AI Job Search Assistant</h1>
          <p className="text-primary-100 text-sm">
            {isConnected ? 'Connected' : 'Connecting...'}
          </p>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={clsx(
              'flex items-start space-x-3',
              message.type === 'user' ? 'justify-end' : 'justify-start'
            )}
          >
            {message.type === 'assistant' && (
              <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <Bot className="w-5 h-5 text-primary-600" />
              </div>
            )}

            <div
              className={clsx(
                'chat-message',
                message.type === 'user' ? 'chat-message-user' : 'chat-message-assistant'
              )}
            >
              <div
                dangerouslySetInnerHTML={{
                  __html: formatMessageContent(message.content)
                }}
              />
              <div className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>

            {message.type === 'user' && (
              <div className="flex-shrink-0 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-gray-600" />
              </div>
            )}
          </div>
        ))}

        {/* Typing indicator */}
        {isLoading && (
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <Bot className="w-5 h-5 text-primary-600" />
            </div>
            <div className="chat-message chat-message-assistant">
              <div className="typing-indicator">
                <div className="typing-dot" style={{ animationDelay: '0ms' }}></div>
                <div className="typing-dot" style={{ animationDelay: '150ms' }}></div>
                <div className="typing-dot" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex space-x-3">
          <textarea
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me about jobs, career advice, or anything else..."
            className="chat-input"
            rows={1}
            disabled={!isConnected}
          />
          <button
            onClick={sendMessage}
            disabled={!inputValue.trim() || !isConnected || isLoading}
            className="chat-button flex items-center space-x-2"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>

        {!isConnected && (
          <p className="text-red-500 text-sm mt-2">
            Connection lost. Attempting to reconnect...
          </p>
        )}
      </div>
    </div>
  )
}
