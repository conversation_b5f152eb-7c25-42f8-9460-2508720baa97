version: '3.8'

services:
  # AI Chatbot Backend (with external OpenSearch)
  chatbot-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chatbot-backend-external
    ports:
      - "8000:8000"
    environment:
      # OpenAI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # External OpenSearch Configuration
      # Option 1: Use OPENSEARCH_URL for full URL (recommended)
      - OPENSEARCH_URL=${OPENSEARCH_URL}
      
      # Option 2: Use individual components (alternative)
      # - OPENSEARCH_HOST=${OPENSEARCH_HOST:-localhost}
      # - OPENSEARCH_PORT=${OPENSEARCH_PORT:-9200}
      # - OPENSEARCH_USE_SSL=${OPENSEARCH_USE_SSL:-true}
      
      # Authentication Options (choose one)
      # Basic Auth
      - OPENSEARCH_USERNAME=${OPENSEARCH_USERNAME}
      - OPENSEARCH_PASSWORD=${OPENSEARCH_PASSWORD}
      
      # API Key Auth (alternative to basic auth)
      # - OPENSEARCH_API_KEY=${OPENSEARCH_API_KEY}
      
      # AWS IAM Auth (for AWS OpenSearch Service)
      - OPENSEARCH_USE_AWS_AUTH=${OPENSEARCH_USE_AWS_AUTH:-false}
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      
      # OpenSearch Settings
      - OPENSEARCH_INDEX=${OPENSEARCH_INDEX:-jobs}
      - OPENSEARCH_VERIFY_CERTS=${OPENSEARCH_VERIFY_CERTS:-true}
      - OPENSEARCH_TIMEOUT=${OPENSEARCH_TIMEOUT:-30}
      
      # Application Settings
      - PORT=8000
      - DEBUG=true
    volumes:
      - ./backend/src:/app/src
    networks:
      - chatbot-network
    restart: unless-stopped

  # AI Chatbot Frontend
  chatbot-frontend:
    image: node:18-alpine
    container_name: chatbot-frontend-external
    working_dir: /app
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_CHATBOT_API_URL=http://localhost:8000
      - NEXT_PUBLIC_CHATBOT_WS_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
    command: sh -c "npm install && npm run dev"
    networks:
      - chatbot-network
    depends_on:
      - chatbot-backend
    restart: unless-stopped

networks:
  chatbot-network:
    driver: bridge
