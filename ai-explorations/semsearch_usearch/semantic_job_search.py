#!/usr/bin/env python3
"""
Semantic Job Search - A tool for embedding, indexing, and searching job listings
using vector similarity search with USearch, optimized for batched processing.
"""

import json
import argparse
import os
import time
from typing import List, Dict, Any, Tuple, Optional, Iterator, Generator
import numpy as np
from tqdm import tqdm
from sentence_transformers import SentenceTransformer
from usearch.index import Index, Matches
from json_repair import repair_json
import traceback
import sys
from geopy.geocoders import Nominatim

# Constants
NUM_EMB_DIM = 384
NUM_GEO_DIM = 3
NUM_TOTAL_DIM = NUM_EMB_DIM + NUM_GEO_DIM
DEFAULT_MODEL = 'intfloat/multilingual-e5-small'
DEFAULT_BATCH_SIZE = 1000

def read_jobs_batched(file_path: str, batch_size: int = DEFAULT_BATCH_SIZE) -> Generator[Dict[int, Dict[str, Any]], None, None]:
    """
    Read a JSONL file and yield batches of parsed JSON documents.
    
    Args:
        file_path (str): Path to the JSONL file
        batch_size (int): Number of jobs to include in each batch
        
    Yields:
        Dict[int, Dict[str, Any]]: Dictionary of parsed JSON documents with job ID as key
    """
    jobs_batch = {}
    job_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_number, line in enumerate(tqdm(file, desc="Reading jobs"), 1):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue
                
                # Fix common JSON issues
                line = line.replace('""', '"')
                line = line.replace('"{', '{')
                line = line.replace('}"', '}')
                
                try:                    
                    job = json.loads(repair_json(line))
                    if isinstance(job, list):
                        job = job[0]
                        
                    try:
                        job_id = int(job['value']['id'])
                    except:
                        print(f"Error extracting ID at line {line_number}")
                        continue
                        
                    jobs_batch[job_id] = job
                    job_count += 1
                    
                    # Yield batch when it reaches the specified size
                    if job_count >= batch_size:
                        yield jobs_batch
                        jobs_batch = {}
                        job_count = 0
                        
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON at line {line_number}: {e}")
                    
    except FileNotFoundError:
        print(f"File not found: {file_path}")
    except Exception as e:
        print(f"Exception: {type(e).__name__} - {e}")
        traceback.print_exc(file=sys.stdout)
    
    # Yield the final batch if it's not empty
    if jobs_batch:
        yield jobs_batch

def get_job_embeddings_batch(
    embedding_model: SentenceTransformer, 
    jobs_batch: Dict[int, Dict[str, Any]]
) -> Tuple[Dict[int, int], np.ndarray]:
    """
    Generate embeddings for a batch of job listings.
    
    Args:
        embedding_model: SentenceTransformer model for generating embeddings
        jobs_batch: Dictionary of job listings in the current batch
        
    Returns:
        Tuple containing:
            - id_map: Dictionary mapping array indices to job IDs
            - job_embeddings: NumPy array of job embeddings
    """
    batch_size = len(jobs_batch)
    job_embeddings = np.zeros((batch_size, NUM_TOTAL_DIM))
    id_map = {}
    
    # Prepare texts for batch encoding
    texts = []
    job_ids = []
    geo_vectors = []
    for idx, (job_id, job) in enumerate(jobs_batch.items()):
        job_title = job['value']['source_title']
        
        # Get employer name from available fields
        employer_name = ""
        if 'value' in job:
            # Check if source_company_name exists in the job value
            if 'source_company_name' in job['value'] and job['value']['source_company_name'] is not None:
                employer_name = job['value']['source_company_name']
            # Try alternative keys based on the codebase
            elif 'enrich_company_name' in job['value'] and job['value']['enrich_company_name'] is not None:
                employer_name = job['value']['enrich_company_name']
            elif 'system_company_name' in job['value'] and job['value']['system_company_name'] is not None:
                employer_name = job['value']['system_company_name']
        
        # create geo vector
        geo_vector = ellipsoid_to_cartesian_deg(job['value']['enrich_geo_point'][1], job['value']['enrich_geo_point'][0])
        if np.any(geo_vector):
            geo_vector = geo_vector / np.linalg.norm(geo_vector)
        # Create passage for embedding
        passage = f"passage: {job_title} {employer_name}"
        texts.append(passage)
        job_ids.append(job_id)
        geo_vectors.append(geo_vector)
        id_map[idx] = job_id
    
    # Batch encode all texts at once (much faster than one by one)
    batch_embeddings = embedding_model.encode(texts, normalize_embeddings=True, show_progress_bar=False)
    
    # Store the embeddings
    for idx, embedding in enumerate(batch_embeddings):
        job_embeddings[idx, :NUM_EMB_DIM] = embedding
        job_embeddings[idx, NUM_EMB_DIM:] = geo_vectors[idx]
    
    return id_map, job_embeddings

def get_job_geo_vectors_batch():
    pass


def process_batches_to_embeddings(
    embedding_model: SentenceTransformer,
    jobs_file: str,
    max_jobs: int = 1000000,
    batch_size: int = DEFAULT_BATCH_SIZE
) ->  Tuple[Dict[int, int], np.ndarray]:
    """
    Process jobs in batches, generate embeddings, and return embeddings.
    
    Args:
        embedding_model: SentenceTransformer model for generating embeddings
        jobs_file: Path to the JSONL file containing job listings
        max_jobs: Maximum number of jobs to process
        batch_size: Number of jobs to process in each batch
        
    Returns:
        Tuple containing:
            - global_id_map: Dictionary mapping array indices to job IDs
            - job_embeddings: NumPy array of job embeddings
    """
    
    global_id_map = {}
    global_count = 0
    job_embeddings = np.empty((max_jobs, NUM_TOTAL_DIM))
    jobs = {}
    # Process batches
    for batch_idx, jobs_batch in enumerate(read_jobs_batched(jobs_file, batch_size)):
        print(f"Processing batch {batch_idx+1} with {len(jobs_batch)} jobs")
        
        # Generate embeddings for the batch
        batch_id_map, batch_embeddings = get_job_embeddings_batch(embedding_model, jobs_batch)
        
        job_embeddings[global_count:global_count + len(batch_embeddings)] = batch_embeddings
        jobs.update(jobs_batch)
        
        # Add to the global ID map with adjusted indices
        for local_idx, job_id in batch_id_map.items():
            global_id_map[global_count + local_idx] = job_id
                
        # Update the global count
        global_count += len(batch_embeddings)
                
        # Check if we've reached the maximum number of jobs
        if global_count >= max_jobs:
            break
    
    
    print(f"Processed {global_count} jobs in total")
    return global_id_map, jobs, job_embeddings

def process_batches(
    embedding_model: SentenceTransformer,
    jobs_file: str,
    index_file: str,
    max_jobs: int = 1000000,
    batch_size: int = DEFAULT_BATCH_SIZE
) -> Tuple[Dict[int, int], Index]:
    """
    Process jobs in batches, generate embeddings, and build an index.
    
    Args:
        embedding_model: SentenceTransformer model for generating embeddings
        jobs_file: Path to the JSONL file containing job listings
        index_file: Path to save the index
        max_jobs: Maximum number of jobs to process
        batch_size: Number of jobs to process in each batch
        
    Returns:
        Tuple containing:
            - global_id_map: Dictionary mapping array indices to job IDs
            - index: USearch index
    """
    # Initialize the index
    index = Index(
        ndim=NUM_TOTAL_DIM,
        metric='cos',
        dtype='f32',
        connectivity=16,
        expansion_add=128,
        expansion_search=64,
    )
    
    global_id_map = {}
    global_count = 0
    
    # Process batches
    for batch_idx, jobs_batch in enumerate(read_jobs_batched(jobs_file, batch_size)):
        print(f"Processing batch {batch_idx+1} with {len(jobs_batch)} jobs")
        
        # Generate embeddings for the batch
        batch_id_map, batch_embeddings = get_job_embeddings_batch(embedding_model, jobs_batch)
        
        # Add to the global ID map with adjusted indices
        for local_idx, job_id in batch_id_map.items():
            global_id_map[global_count + local_idx] = job_id
        
        # Add batch embeddings to the index
        batch_keys = np.arange(global_count, global_count + len(batch_embeddings))
        index.add(batch_keys, batch_embeddings)
        
        # Update the global count
        global_count += len(batch_embeddings)
        
        # Save the index after each batch (optional, can be commented out for speed)
        if batch_idx % 10 == 0:
            save_index(index, index_file)
            save_id_map(global_id_map, f"{os.path.splitext(index_file)[0]}_id_map.json")
        
        # Check if we've reached the maximum number of jobs
        if global_count >= max_jobs:
            break
    
    # Final save
    save_index(index, index_file)
    save_id_map(global_id_map, f"{os.path.splitext(index_file)[0]}_id_map.json")
    
    print(f"Processed {global_count} jobs in total")
    return global_id_map, index

def get_query_embedding(embedding_model: SentenceTransformer, query: str, query_geo_vector: np.ndarray) -> np.ndarray:
    """
    Generate embedding for a search query.
    
    Args:
        embedding_model: SentenceTransformer model for generating embeddings
        query: Search query text
        
    Returns:
        NumPy array containing the query embedding
    """
    query = f"query: {query}"
    query_embedding = embedding_model.encode(query, normalize_embeddings=True)
    
    # Normalize the geo vector
    if np.any(query_geo_vector):
        query_geo_vector = query_geo_vector / np.linalg.norm(query_geo_vector)
    
    # Concatenate and ensure the final vector is normalized
    query_embedding = np.concatenate((query_embedding, query_geo_vector))
    return query_embedding

def ellipsoid_to_cartesian_deg(lat_deg, lon_deg, a=6378137.0, b=6356752.314245):   
   lon = np.radians(lon_deg)
   lat = np.radians(lat_deg)
   x = a * np.cos(lat) * np.cos(lon)
   y = a * np.cos(lat) * np.sin(lon)
   z = b * np.sin(lat)
   return np.array([x, y, z])

def save_index(index: Index, file_path: str) -> None:
    """
    Save the USearch index to a file.
    
    Args:
        index: USearch index
        file_path: Path to save the index
    """
    index.save(file_path)
    print(f"Index saved to {file_path} with {len(index)} entries")

def load_index(file_path: str, ndim: int = NUM_EMB_DIM) -> Index:
    """
    Load a USearch index from a file.
    
    Args:
        file_path: Path to the index file
        ndim: Number of dimensions in the index
        
    Returns:
        USearch index
    """
    index = Index(ndim=ndim)
    index.load(file_path)
    print(f"Index loaded from {file_path} with {len(index)} entries")
    return index

def save_id_map(id_map: Dict[int, int], file_path: str) -> None:
    """
    Save the ID map to a JSON file.
    
    Args:
        id_map: Dictionary mapping array indices to job IDs
        file_path: Path to save the ID map
    """
    # Convert keys to strings for JSON serialization
    serializable_map = {str(k): v for k, v in id_map.items()}
    with open(file_path, 'w') as f:
        json.dump(serializable_map, f)
    print(f"ID map saved to {file_path}")

def load_id_map(file_path: str) -> Dict[int, int]:
    """
    Load the ID map from a JSON file.
    
    Args:
        file_path: Path to the ID map file
        
    Returns:
        Dictionary mapping array indices to job IDs
    """
    with open(file_path, 'r') as f:
        serializable_map = json.load(f)
    # Convert keys back to integers
    id_map = {int(k): v for k, v in serializable_map.items()}
    print(f"ID map loaded from {file_path}")
    return id_map

def search_jobs(
    index: Index, 
    query_embedding: np.ndarray, 
    id_map: Dict[int, int], 
    jobs_file: str,
    num_results: int = 10
) -> List[Dict[str, Any]]:
    """
    Search for jobs using a query embedding.
    
    Args:
        index: USearch index
        query_embedding: NumPy array containing the query embedding
        id_map: Dictionary mapping array indices to job IDs
        jobs_file: Path to the JSONL file containing job listings
        num_results: Number of results to return
        
    Returns:
        List of search results with job details and similarity scores
    """
    vector = np.array(query_embedding)
    matches: Matches = index.search(vector, num_results)
    
    # Get the job IDs we need to look up
    job_ids_to_find = [id_map[match.key] for match in matches]
    
    # Find the jobs in the file
    found_jobs = {}
    
    # Read the file and find the jobs we need
    with open(jobs_file, 'r', encoding='utf-8') as file:
        for line in tqdm(file, desc="Finding matching jobs"):
            line = line.strip()
            if not line:
                continue
                
            try:
                line = line.replace('""', '"')
                line = line.replace('"{', '{')
                line = line.replace('}"', '}')
                job = json.loads(repair_json(line))
                
                if isinstance(job, list):
                    job = job[0]
                    
                try:
                    job_id = int(job['value']['id'])
                    if job_id in job_ids_to_find:
                        found_jobs[job_id] = job
                        
                        # If we found all the jobs we need, we can stop
                        if len(found_jobs) == len(job_ids_to_find):
                            break
                except:
                    continue
            except:
                continue
    
    # Build the results
    results = []
    for match in matches:
        job_id = id_map[match.key]
        
        if job_id not in found_jobs:
            print(f"Warning: Job ID {job_id} not found in the file")
            continue
            
        job = found_jobs[job_id]
        
        # Get employer name safely
        employer_name = "Unknown"
        if 'source_company_name' in job['value']:
            employer_name = job['value']['source_company_name']
        elif 'enrich_company_name' in job['value']:
            employer_name = job['value']['enrich_company_name']
        elif 'system_company_name' in job['value']:
            employer_name = job['value']['system_company_name']
        
        result = {
            "id": job_id,
            "key": match.key,
            "job_title": job['value']['source_title'],
            "employer_name": employer_name,
            "score": 1.0 - match.distance,
            "job_data": job
        }
        results.append(result)
    
    return results

def interactive_query_mode(
    embedding_model: SentenceTransformer,
    index: Index,
    id_map: Dict[int, int],
    jobs_file: str,
    num_results: int = 10
):
    """
    Run an interactive session allowing the user to input multiple search queries.
    
    Args:
        embedding_model: SentenceTransformer model for generating embeddings
        index: USearch index
        id_map: Dictionary mapping array indices to job IDs
        jobs_file: Path to the JSONL file containing job listings
        num_results: Number of search results to return
    """
    print("\n=== Interactive Job Search Mode ===")
    print("Type 'exit' or 'quit' to end the session")
    
    # Get location once at the beginning
    location = input("\nEnter your location (e.g., 'Mountain View, CA'): ")
    
    # Set up geocoder
    geolocator = Nominatim(user_agent="usearch")
    try:
        geolocation = geolocator.geocode(location)
        if geolocation:
            print(f"Location found: {geolocation.address}")
            query_geo_vector = ellipsoid_to_cartesian_deg(geolocation.latitude, geolocation.longitude)
        else:
            print("Location not found. Using default (0,0,0) coordinates.")
            query_geo_vector = np.zeros(3)
    except Exception as e:
        print(f"Error geocoding location: {e}")
        print("Using default (0,0,0) coordinates.")
        query_geo_vector = np.zeros(3)
    
    while True:
        # Get user input
        user_query = input("\nEnter your job search query: ")
        
        # Check if user wants to exit
        if user_query.lower() in ['exit', 'quit']:
            print("Ending interactive session.")
            break
        
        # Skip empty queries
        if not user_query.strip():
            continue
        
        # Process the query
        print("\nProcessing query...")
        try:
            query_embedding = get_query_embedding(embedding_model, user_query, query_geo_vector)
            
            # Ensure the query embedding is L2 normalized
            query_embedding = query_embedding / np.linalg.norm(query_embedding)
            
            print(f"Query embedding shape: {query_embedding.shape}, norm: {np.linalg.norm(query_embedding):.4f}")
            
            results = search_jobs(index, query_embedding, id_map, jobs_file, num_results)
            
            print("\nSearch Results:")
            print("=" * 80)
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['job_title']} - {result['employer_name']} - {result['job_data']['value']['source_location']}")
                print(f"   Score: {result['score']:.4f}")
                print(f"   Job ID: {result['id']}")
                print("-" * 80)
        except Exception as e:
            print(f"Error processing query: {str(e)}")

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description='Semantic Job Search with Batched Processing')
    parser.add_argument('--jobs-file', type=str, required=True, help='Path to the JSONL file containing job listings')
    parser.add_argument('--index-file', type=str, default='jobs_index.usearch', help='Path to save/load the index')
    parser.add_argument('--model', type=str, default=DEFAULT_MODEL, help='SentenceTransformer model to use')
    parser.add_argument('--max-jobs', type=int, default=1000000, help='Maximum number of jobs to process')
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE, help='Number of jobs to process in each batch')
    parser.add_argument('--query', type=str, help='Search query (if not provided, only indexing will be performed)')
    parser.add_argument('--num-results', type=int, default=10, help='Number of search results to return')
    parser.add_argument('--rebuild-index', action='store_true', help='Force rebuilding the index even if it exists')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive query mode')
    parser.add_argument('--location', type=str, default='Mountain View, CA', help='Location for job search (used with --query)')
    
    args = parser.parse_args()
    
    # Load the embedding model
    print(f"Loading embedding model: {args.model}")
    embedding_model = SentenceTransformer(args.model)
    
    # Check if index exists and should be loaded
    index_exists = os.path.exists(args.index_file)
    id_map_file = f"{os.path.splitext(args.index_file)[0]}_id_map.json"
    id_map_exists = os.path.exists(id_map_file)
    
    if index_exists and id_map_exists and not args.rebuild_index:
        print(f"Loading existing index from {args.index_file}")
        index = load_index(args.index_file)
        id_map = load_id_map(id_map_file)
    else:
        # Process jobs in batches and build the index
        print(f"Processing jobs in batches of {args.batch_size}")
        id_map, index = process_batches(
            embedding_model, 
            args.jobs_file, 
            args.index_file, 
            args.max_jobs, 
            args.batch_size
        )
    
    # Run in interactive mode if requested
    if args.interactive:
        interactive_query_mode(
            embedding_model,
            index,
            id_map,
            args.jobs_file,
            args.num_results
        )
    # Perform search if query is provided
    elif args.query:
        location = args.location
        print(f"Searching for: {args.query} at {location}")
        
        geolocator = Nominatim(user_agent="usearch")
        try:
            geolocation = geolocator.geocode(location)
            if geolocation:
                query_geo_vector = ellipsoid_to_cartesian_deg(geolocation.latitude, geolocation.longitude)
            else:
                print(f"Location '{location}' not found. Using default (0,0,0) coordinates.")
                query_geo_vector = np.zeros(3)
        except Exception as e:
            print(f"Error geocoding location '{location}': {e}")
            query_geo_vector = np.zeros(3)
        
        query_embedding = get_query_embedding(embedding_model, args.query, query_geo_vector)
        
        # Ensure the query embedding is L2 normalized
        query_embedding = query_embedding / np.linalg.norm(query_embedding)
        
        print(f"Query embedding shape: {query_embedding.shape}, norm: {np.linalg.norm(query_embedding):.4f}")
        
        results = search_jobs(index, query_embedding, id_map, args.jobs_file, args.num_results)
        
        print("\nSearch Results:")
        print("=" * 80)
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['job_title']} - {result['employer_name']} - {result['job_data']['value']['source_location']}")
            print(f"   Score: {result['score']:.4f}")
            print(f"   Job ID: {result['id']}")
            print("-" * 80)

if __name__ == "__main__":
    main()
