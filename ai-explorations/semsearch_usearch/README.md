# Semantic Job Search

A tool for embedding, indexing, and searching job listings using vector similarity search with USearch, optimized for batched processing.

## Overview

This tool allows you to:
- Process large JSONL files of job listings in efficient batches
- Generate embeddings for job listings using sentence transformers
- Create and save vector search indexes
- Perform semantic searches for jobs based on natural language queries
- Use location-aware search with geographic coordinates
- Run in interactive mode for multiple consecutive searches

## Requirements

- Python 3.6+
- Dependencies:
  - sentence_transformers
  - usearch
  - numpy
  - tqdm
  - json_repair
  - geopy

## Usage

### Basic Usage

```bash
python semantic_job_search.py --jobs-file path/to/jobs.jsonl --index-file jobs_index.usearch --query "software engineer"
```

### Interactive Mode

```bash
python semantic_job_search.py --jobs-file path/to/jobs.jsonl --interactive
```

### Parameters

- `--jobs-file`: Path to the JSONL file containing job listings (required)
- `--index-file`: Path to save/load the index (default: jobs_index.usearch)
- `--model`: SentenceTransformer model to use (default: intfloat/multilingual-e5-small)
- `--max-jobs`: Maximum number of jobs to process (default: 10000)
- `--query`: Search query (if not provided, only indexing will be performed)
- `--num-results`: Number of search results to return (default: 10)
- `--batch-size`: Number of jobs to process in each batch (default: 1000)
- `--rebuild-index`: Force rebuilding the index even if it exists
- `--interactive`: Run in interactive query mode
- `--location`: Location for job search (default: "Mountain View, CA", used with --query)

## Features

- **Batched Processing**: Efficiently handles large datasets by processing jobs in batches
- **JSON Repair**: Automatically fixes common JSON formatting issues
- **Optimized Embedding**: Uses batch encoding for faster processing
- **L2 Normalization**: Ensures consistent vector normalization for improved search accuracy
  - Both query and job vectors are L2 normalized for optimal cosine similarity matching
  - Geo vectors are separately normalized before concatenation with text embeddings
- **Location-Aware Search**: Incorporates geographic coordinates for location-based relevance
  - Converts location strings to latitude/longitude using geocoding
  - Transforms coordinates to 3D Cartesian vectors for better similarity calculations
- **Enhanced Interactive Mode**: 
  - Run multiple searches in a single session without reloading the model or index
  - Set your location once and perform multiple queries
  - Simple command interface with clear result formatting
- **Flexible Search**: Find semantically similar job listings based on natural language queries

## Technical Implementation

The tool implements several optimizations for efficient processing:

1. **Batched Processing**: Large JSONL files are processed in configurable batches to minimize memory usage
2. **Vector Normalization**: All embeddings are L2-normalized to ensure consistent similarity calculations
3. **Efficient Index Storage**: The USearch index and ID mapping are saved separately for faster loading
4. **Geocoding Cache**: Location coordinates are cached to reduce redundant API calls
5. **Error Handling**: Robust error handling for malformed JSON and geocoding failures

## Example Workflow

1. **Index Creation**: Process job listings and create a searchable vector index
2. **Query Embedding**: Convert natural language queries to vector embeddings
3. **Vector Similarity**: Find the most similar job listings using cosine similarity
4. **Result Ranking**: Rank and return the most relevant job listings
