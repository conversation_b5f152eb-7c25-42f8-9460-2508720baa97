# Ad Bid Service Algorithmic Flow

## Overview
This document outlines the algorithmic flow of the Ad Bid Service as implemented in `ad_bid_service.go`. The service is responsible for calculating bids for job advertisements based on various rules and parameters.

## Service Structure
The Ad Bid Service is implemented as a Go service with the following key components:

1. **Service Interfaces**:
   - `AdBidServiceInterface`: Main interface for bid calculation
   - `JobServiceInterface`: Interface for job data retrieval

2. **Service Implementation**:
   - `adBidService`: Core implementation containing all the business logic
   - `adBidServiceGrpcImpl`: gRPC wrapper for the service

## Algorithmic Flow

### 1. Initialization
- Service is initialized with various rule services:
  - Feedcode Rules Service
  - Channel Rules Service
  - ECPC Service
  - Country Rules Service
  - Max PPC Service
  - Campaign PPC Modifier Service
  - Bid Tax Service
  - Budget Rules Service

### 2. Bid Calculation Process
The main bid calculation flow is implemented in the `CalculateBids` method and follows these steps:

#### 2.1. Request Processing
- Receive job IDs and parameters (channel, partner, keyword, etc.)
- Retrieve job data from the job store

#### 2.2. Initial Data Assignment
- Convert job data to processing format
- Set initial PPC (Pay Per Click) values
- Initialize currency rates and discounts

#### 2.3. Job Processing Pipeline
Each job goes through a sequential processing pipeline:

1. **Campaign Modifier Application**
   - Apply campaign-specific PPC modifiers
   - Filter jobs based on campaign rules

2. **Exposure Rules Processing**
   - Apply feedcode-based exposure rules
   - Determine if job should be displayed based on exposure settings

3. **Budget Rules Processing**
   - Apply budget constraints
   - Filter jobs that exceed budget limits

4. **ECPC (Effective Cost Per Click) Processing**
   - Calculate and apply ECPC rules
   - Adjust bid values based on click performance

5. **Max PPC Processing**
   - Apply maximum PPC constraints
   - Set upper limits on bid values

6. **Channel Rules Processing**
   - Apply channel-specific rules
   - Adjust bids based on distribution channel

7. **Country Rules Processing**
   - Apply country-specific rules
   - Adjust bids based on geographic targeting

8. **Bid Tax Processing**
   - Calculate and apply bid share (tax)
   - Finalize bid values

#### 2.4. Result Compilation
- Compile processed job results
- Calculate final bid values
- Format response with bid results

### 3. Prefiltering Process
The service also implements prefiltering to quickly determine if jobs should be considered:

- `PrefilterV2`: HTTP-based prefiltering
- `Prefilter`: gRPC-based prefiltering
- Both apply feedcode rules to determine job eligibility

### 4. Telemetry and Monitoring
Throughout the process, the service:
- Records processing times for each step
- Tracks filtered jobs at each stage
- Monitors total requests and errors
- Reports metrics via OpenTelemetry

### 5. Event Processing
- Events are collected and processed asynchronously
- A dedicated goroutine handles event publishing

## Key Algorithms

### Bid Calculation
The core bid calculation involves:
1. Starting with initial PPC values
2. Applying various modifiers (campaign, channel, country)
3. Applying constraints (budget, max PPC)
4. Calculating final bid values

### Exposure Determination
Exposure rules determine if and where jobs should be displayed:
1. Feedcode rules define basic eligibility
2. Channel rules specify distribution channels
3. Country rules apply geographic targeting

### Budget Management
Budget rules ensure spending limits are respected:
1. Campaign-level budget constraints
2. Job-level budget allocation
3. Dynamic adjustment based on performance

## Conclusion
The Ad Bid Service implements a comprehensive pipeline for processing job advertisements and calculating optimal bid values. The modular design allows for flexible rule application and efficient processing of large volumes of job data.
