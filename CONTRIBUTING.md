# Contributing to AI Job Search Chatbot

Thank you for your interest in contributing to the AI Job Search Chatbot! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Docker & Docker Compose
- Node.js 18+
- Python 3.9+
- Git

### Development Setup
1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/your-username/ai-job-search-chatbot.git
   cd ai-job-search-chatbot
   ```
3. Set up environment:
   ```bash
   cp .env.example .env
   # Add your OpenAI API key to .env
   ```
4. Start development environment:
   ```bash
   docker-compose -f docker-compose.chatbot.yml up -d
   ```

## 🛠️ Development Workflow

### Branch Naming
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Commit Messages
Use conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

Examples:
```
feat(search): add fuzzy matching for job titles
fix(websocket): resolve connection timeout issues
docs(readme): update installation instructions
```

## 🏗️ Project Structure

### Frontend (`packages/frontend/services/ai-chatbot/`)
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **WebSocket** for real-time communication

### Backend (`packages/backend/services/ai-chatbot/`)
- **FastAPI** with Python 3.9+
- **OpenSearch** integration
- **OpenAI** API integration
- **WebSocket** support

## 🧪 Testing

### Backend Tests
```bash
cd packages/backend/services/ai-chatbot
pip install -r requirements.txt
pytest
```

### Frontend Tests
```bash
cd packages/frontend/services/ai-chatbot
npm install
npm test
```

### Integration Tests
```bash
# Start services
docker-compose -f docker-compose.chatbot.yml up -d

# Run integration tests
python test_integration.py
```

## 📝 Code Style

### Python
- Follow **PEP 8**
- Use **type hints**
- Maximum line length: 88 characters
- Use **black** for formatting:
  ```bash
  pip install black
  black src/
  ```

### TypeScript/React
- Follow **Prettier** configuration
- Use **ESLint** rules
- Prefer functional components with hooks
- Format with Prettier:
  ```bash
  npm run format
  ```

## 🔍 Pull Request Process

1. **Create a feature branch** from `main`
2. **Make your changes** with clear, focused commits
3. **Add tests** for new functionality
4. **Update documentation** if needed
5. **Ensure all tests pass**
6. **Submit a pull request** with:
   - Clear title and description
   - Reference to related issues
   - Screenshots for UI changes
   - Testing instructions

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Refactoring

## Testing
- [ ] Tests pass locally
- [ ] Added new tests
- [ ] Manual testing completed

## Screenshots (if applicable)

## Related Issues
Fixes #(issue number)
```

## 🐛 Bug Reports

Use the GitHub issue template:
- **Clear title** describing the issue
- **Steps to reproduce** the problem
- **Expected vs actual behavior**
- **Environment details** (OS, Docker version, etc.)
- **Logs or error messages**
- **Screenshots** if applicable

## 💡 Feature Requests

For new features:
- **Use case description** - Why is this needed?
- **Proposed solution** - How should it work?
- **Alternatives considered** - Other approaches?
- **Additional context** - Mockups, examples, etc.

## 🏷️ Issue Labels

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements to docs
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed
- `question` - Further information requested

## 🔧 Development Tips

### Local Development
```bash
# Backend only
cd packages/backend/services/ai-chatbot
python src/main.py

# Frontend only
cd packages/frontend/services/ai-chatbot
npm run dev

# Full stack with hot reload
docker-compose -f docker-compose.chatbot.yml up
```

### Debugging
- Backend logs: `docker-compose logs chatbot-backend -f`
- Frontend logs: Check browser console
- OpenSearch: http://localhost:5601 (Dashboards)

### Adding New Dependencies

#### Python
```bash
# Add to requirements.txt
echo "new-package==1.0.0" >> requirements.txt

# Rebuild container
docker-compose build chatbot-backend
```

#### Node.js
```bash
# Add package
cd packages/frontend/services/ai-chatbot
npm install new-package

# Rebuild container
docker-compose build chatbot-frontend
```

## 📚 Resources

### Documentation
- [FastAPI Docs](https://fastapi.tiangolo.com/)
- [React Docs](https://react.dev/)
- [OpenSearch Docs](https://opensearch.org/docs/)
- [OpenAI API Docs](https://platform.openai.com/docs/)

### Tools
- [Docker](https://docs.docker.com/)
- [TypeScript](https://www.typescriptlang.org/docs/)
- [Tailwind CSS](https://tailwindcss.com/docs)

## 🤝 Community

- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and best practices
- Follow the [Code of Conduct](CODE_OF_CONDUCT.md)

## 📞 Getting Help

- **GitHub Issues** - For bugs and feature requests
- **Discussions** - For questions and general discussion
- **Documentation** - Check README and inline comments

Thank you for contributing! 🎉
