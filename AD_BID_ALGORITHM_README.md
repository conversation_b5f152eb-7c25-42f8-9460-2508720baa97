# Ad Bid Processing Algorithm Flow

## 1. Campaign Modifier (BudgetRulesService.GetCampaignModifier)

**Purpose**: Adjusts the initial PPC (Pay-Per-Click) values based on campaign-specific modifiers, discounts, and currency rates.

**Algorithm Steps**:
- Convert campaign ID from string to int
- Check if campaign exists in data store
- If found:
  - Apply campaign PPC modifier: InitialPpc *= CampaignPPCModifier
  - Apply campaign PPC modifier: InitialPpcU *= CampaignPPCModifier
  - Apply discount if available (PPCDiscount != 1 && > 0)
  - Apply currency rate if available
- Record explanation data for debugging

## 2. Exposure Rules (FeedcodeRulesService.GetAdsExposure)

**Purpose**: Determines the exposure percentage for a job, which affects whether it will be displayed to users.

**Algorithm Steps**:
- Create filter map with job attributes (Feedcode, CampaignID, Channel, etc.)
- Search the feedcode rules tree for matching rules
- If match found:
  - Check CemStatus and SetCemExposure values
  - Set job.Exposure based on matching rule
  - Default to 100 if no valid exposure found
- Record explanation data
- Call tools.AssignAdBidResults to determine final display status

## 3. Budget Rules (BudgetRulesService.GetAdsBudgetRules)

**Purpose**: Ensures that jobs from campaigns with budget issues (inactive, depleted) are not displayed or have their bids adjusted accordingly.

**Algorithm Steps**:
- Convert campaign/job IDs from string to int
- Check if job-level budget exists
  - If yes, set job.Ppc/PpcU/etc. to 0 and return
- Check if campaign exists in data store
  - If found, check campaign and account status
    - If not active, set job.Ppc/PpcU/etc. to 0
    - If AcrProfitProtection enabled, apply auto campaign rules
  - If not found, set job.Ppc/PpcU/etc. to 0
- Record explanation data

## 4. ECPC Rules (EcpcService.GetAdsECPC)

**Purpose**: Applies an ECPC (Effective Cost Per Click) modifier that can adjust the final CPC calculation based on performance data.

**Algorithm Steps**:
- Create filter map with Country, Channel, Partner
- Search ECPC rules tree for matching rules
- If match found with non-zero ECPC:
  - Set job.ECPC to the matched value
- Otherwise, set job.ECPC to 1.0 (default)
- Cap ECPC at 2.0 maximum

## 5. SetMaxPPC Rules (FeedcodeRulesService.GetSetMaxPPC)

**Purpose**: Enforces maximum PPC values for specific feedcodes, campaigns, or other job attributes.

**Algorithm Steps**:
- Create filter map with job attributes
- Search the setMaxPpc rules tree for matching rules
- If match found and valid:
  - Convert SetMaxPpc to integer
  - Set job.Ppc to this value
  - Recalculate job.PpcU based on currency rate
- Record explanation data

## 6. MaxPPC Rules (MaxPPCService.GetAdsMaxPPC)

**Purpose**: Applies the ECPC modifier to calculate the final CPC (Cost Per Click) value based on the PPC and bidshare.

**Algorithm Steps**:
- Set job.Ppc = job.InitialPpc
- Set job.PpcU = job.InitialPpcU
- If job.ECPC > 1.0:
  - Calculate job.Cpc = job.InitialPpc * job.Bidshare / 100
- Else if channel != "partnerApi":
  - Set job.MaxPPCApplied = true
  - Calculate job.Cpc = job.InitialPpc * job.ECPC * job.Bidshare / 100

## 7. Channel Rules (ChannelRulesService.GetAdsChannelInfo)

**Purpose**: Applies channel-specific rules that affect bidshare percentages, maximum PPC values, and exposure settings.

**Algorithm Steps**:
- Create filter map with job attributes
- Search channel rules tree for matching rules
- Apply rules in sequence:
  - Apply bidshare rules:
    - Set bidshare from rule or default (70 for partnerXML, 75 for partnerApi)
    - Calculate Cpc = Ppc * modifier * bidshare / 100
  - Apply organic PpcU rules if available
  - Apply maxPpcU rules:
    - If PpcU > maxPpcU, cap PpcU and recalculate Ppc
  - Apply exposure rules if needed
- Record explanation data

## 8. Country Rules (CountryRulesService.GetAdsCountryRules)

**Purpose**: Applies country-specific rules that may adjust exposure or maximum PPC values based on the job's country.

**Algorithm Steps**:
- Create filter with job.Country
- Search country rules tree for matching rules
- If match found:
  - Check if job.PpcU < JobPpcuLessThan:
    - If true, set job.Exposure to SetExposure value
  - Check if SetMaxPPCU is defined:
    - If job.PpcU > SetMaxPPCU, cap PpcU and recalculate Ppc
- Call tools.AssignAdBidResults to update display status

## 9. Bidshare/BidTax Rules (BidTaxService.GetAdsBidshare)

**Purpose**: Applies a progressive tax to bids that exceed certain thresholds, reducing the effective bidshare for high bids to optimize revenue.

**Algorithm Steps**:
- Get current bidshare value
- Create filter with Country, Channel, Partner
- Search bidtax rules tree for matching rules
- If match found with non-zero Max value:
  - Calculate current CPC = (PpcU/10) * (bidshare/100)
  - Calculate new bid using tax brackets:
    - Get median and max values from rule
    - Apply progressive tax brackets to amount above median
  - Calculate new bidshare = (newBid / (PpcU/10)) * 100
  - Update job.Bidshare = newBidshare
  - Recalculate job.Cpc with new bidshare
- Record explanation data

## Final Processing for Partner Channels

**Purpose**: Performs final currency conversions and applies any experimental treatments from Statsig before returning the final bid result.

**Algorithm Steps**:
- If channel is "partnerXML" or "partnerApi":
  - Calculate CpcU = Cpc * CurrencyRate * (1 - Discount) * 10
  - Check for Statsig feature gates/experiments if enabled
    - Apply experimental bidshare/other values if in test group
- Create final JobBidResult with all calculated values

## Complete Flow Summary

1. **Start**: Job with initial PPC values enters the system
2. **Campaign Modifier**: Adjust initial PPC based on campaign settings
3. **Exposure Rules**: Determine if job should be displayed based on feedcode
4. **Budget Rules**: Check campaign budget status and adjust accordingly
5. **ECPC Rules**: Apply performance-based CPC modifiers
6. **SetMaxPPC Rules**: Apply feedcode-specific maximum PPC values
7. **MaxPPC Rules**: Calculate CPC based on PPC, ECPC, and bidshare
8. **Channel Rules**: Apply channel-specific bidshare and other settings
9. **Country Rules**: Apply country-specific exposure and PPC caps
10. **Bidshare/BidTax**: Apply progressive tax to high bids
11. **Final Processing**: Convert currencies and apply experiments
12. **Result**: Return final bid values (PPC, CPC, exposure, display status)

Each step can potentially filter out jobs (by setting display=false) or adjust bid values to optimize for revenue and performance.