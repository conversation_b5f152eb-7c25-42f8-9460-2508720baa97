#!/bin/bash

# AI Chatbot Startup Script
# This script sets up and starts the AI chatbot services

set -e

echo "🤖 Starting AI Job Search Chatbot Setup..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from template..."
    echo "OPENAI_API_KEY=your_openai_api_key_here" > .env
    echo "📝 Please edit .env file with your OpenAI API key before continuing."
    echo "   You can get an API key from: https://platform.openai.com/api-keys"
    read -p "Press Enter after you've updated the .env file..."
fi

# Source environment variables
source .env

# Check if OpenAI API key is set
if [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ] || [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ Please set your OpenAI API key in the .env file"
    exit 1
fi

echo "✅ Environment configuration looks good!"

# Stop any existing services
echo "🛑 Stopping any existing services..."
docker-compose -f docker-compose.chatbot.yml down

# Start services with Docker Compose
echo "🚀 Starting services..."
docker-compose -f docker-compose.chatbot.yml up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
if docker-compose -f docker-compose.chatbot.yml ps | grep -q "Up"; then
    echo "✅ Services are running!"
else
    echo "❌ Some services failed to start. Check logs with:"
    echo "   docker-compose -f docker-compose.chatbot.yml logs"
    exit 1
fi

# Wait for OpenSearch to be ready
echo "⏳ Waiting for OpenSearch to be ready..."
timeout=60
counter=0
while ! curl -s http://localhost:9200 > /dev/null; do
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "❌ OpenSearch failed to start within $timeout seconds"
        exit 1
    fi
done

echo "✅ OpenSearch is ready!"

# Seed OpenSearch with sample data
echo "📊 Seeding OpenSearch with sample job data..."
docker-compose -f docker-compose.chatbot.yml exec chatbot-backend python src/scripts/seed_opensearch.py

# Display service URLs
echo ""
echo "🎉 AI Job Search Chatbot is ready!"
echo ""
echo "📱 Frontend (Chat Interface): http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "🔍 OpenSearch: http://localhost:9200"
echo "📊 OpenSearch Dashboards: http://localhost:5601"
echo ""
echo "💡 Try asking the chatbot:"
echo "   - 'Find software engineer jobs in San Francisco'"
echo "   - 'Show me remote Python developer positions'"
echo "   - 'I'm looking for data science jobs'"
echo ""
echo "🛑 To stop all services: docker-compose -f docker-compose.chatbot.yml down"
echo "📋 To view logs: docker-compose -f docker-compose.chatbot.yml logs -f"
