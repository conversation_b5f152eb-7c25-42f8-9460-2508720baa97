import requests
from bs4 import BeautifulSoup
import json
import logging
import boto3
from botocore.exceptions import ClientError
from enum import Enum
import math
import csv
from json_repair import repair_json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
region="us-east-1"
accept = 'application/json'
contentType = 'application/json'
SS_API_ENDPOINT='/api/v1/tools/playground'

class PLAYGROUND_URL(Enum):
    SEARCH_SERVICE='http://search-service.apps.talent.com' + SS_API_ENDPOINT
    LOCAL_HOST='http://localhost:8001' + SS_API_ENDPOINT

class LLMS_AS_JUDGE(Enum):
    LLAMA_3_3_70B = 'us.meta.llama3-3-70b-instruct-v1:0'
    DEEPSEEK_R1 = 'deepseek.r1-v1:0'
    CLAUDE_3_7 = 'anthropic.claude-3-7-sonnet-20250219-v1:0'


def load_playground_queries(filename="playground_queries.csv"):
    """
    Loads playground queries from a CSV file into a dictionary.
    Expected CSV format: keyword,location,country
    
    Args:
        filename (str): Path to the CSV file containing playground queries
        
    Returns:
        list[dict]: List of dictionaries containing query parameters
    """
    queries = []
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                keyword = row['keyword']
                config_retriever_name = row["config_retriever_name"]
                structured_data = json.loads(row['structured_data'])
                location = structured_data['geo']['formatted_address']
                countrycode = structured_data['geo']['countrycode']
                query = {
                    'keyword': keyword.strip(),
                    'location': location.strip(),
                    'country': countrycode.strip(),  # Default to 'us' if not specified
                }
                queries.append(query)
        return queries
    except FileNotFoundError:
        print(f"Error: Could not find file {filename}")
        return []
    except Exception as e:
        print(f"Error loading queries: {str(e)}")
        return []

def get_list_of_foundation_models(bedrock_client):
    """
    Gets a list of available Amazon Bedrock foundation models.
    :return: The list of available bedrock foundation models.
    """
    try:
        response = bedrock_client.list_foundation_models()
        models = response["modelSummaries"]
        logger.info("Got %s foundation models.", len(models))
        return models

    except ClientError:
        logger.error("Couldn't list foundation models.")
        raise
    return

def list_foundation_models():
    """Entry point for the example. Uses the AWS SDK for Python (Boto3)
    to create an Amazon Bedrock client. Then lists the available Bedrock models
    in the region set in the callers profile and credentials.
    """

    bedrock_client = boto3.client(service_name="bedrock")
    fm_models = get_list_of_foundation_models(bedrock_client)
    for model in fm_models:
        print(f"Model: {model['modelName']}")
        print(f"Model: {model['modelId']}")
        #print(json.dumps(model, indent=2))
        #print("---------------------------\n")

    logger.info("Done.")
    return

def get_llama(llms_as_judge):
    return

def get_deepseek(llms_as_judge):
    return

def get_llama_to_judge(llms_as_judge, message):
    
    # Set the model ID, e.g., Llama 3 70b Instruct.
    model_id = llms_as_judge.value

    # Embed the prompt in Llama 3's instruction format.
    formatted_prompt = f"""
    <|begin_of_text|><|start_header_id|>user<|end_header_id|>
    {message}
    <|eot_id|>
    <|start_header_id|>assistant<|end_header_id|>
    """
    # Format the request payload using the model's native structure.
    native_request = {
        "prompt": formatted_prompt,
        "max_gen_len": 512,
        "temperature": 0.5,
    }

    model_response = invoke_llm_request(model_id, native_request)
    return model_response

def get_openai(llms_as_judge):
    return


def invoke_llm_request(model_id, native_request):
    # Convert the native request to JSON.
    request = json.dumps(native_request)
    
    # Create a Bedrock Runtime client in the AWS Region of your choice.
    client = boto3.client("bedrock-runtime", region_name=region)

    try:
        # Invoke the model with the request.
        response = client.invoke_model(modelId=model_id, body=request, accept=accept, contentType=contentType)
    
    except (ClientError, Exception) as e:
        print(f"ERROR: Can't invoke '{model_id}'. Reason: {e}")
        exit(1)
    
    # Decode the response body.
    model_response = response["body"].read()
    return model_response


QUERY_ANALYSIS_PROMPT = '''
 You are an employment, job, and labor market expert with deep knowledge of the USA job market. The user gives you a query. Elaborate on this
 query to explain its meaning. 
 
Analyze and classify the query into the following categories. Do your best to always return a query type or return unknown.
    1. job_title_query
    2. company_name_query
    3. job_type_query
 
 For job title query, further classify the query into one of the 23 SOC major groups with their titles and SOC code.
 Here are the 23 SOC major groups:
     1. Management, business, and financial occupations (11)
     2. Professional and related occupations (13)
     3. Service occupations (25)
     4. Sales and related occupations (31)
     5. Office and administrative support occupations (33)
     6. Farming, fishing, and forestry occupations (35)
     7. Construction and extraction occupations (37)
     8. Installation, maintenance, and repair occupations (39)
     9. Production occupations (41)
     10. Transportation and material moving occupations (43)
     11. Architecture and engineering occupations (15)
     12. Life, physical, and social science occupations (17)
     13. Community and social service occupations (21)
     14. Legal occupations (23)
     15. Education, training, and library occupations (25)
     16. Arts, design, entertainment, sports, and media occupations (27)
     17. Healthcare practitioners and technical occupations (29)
     18. Healthcare support occupations (31)
     19. Protective service occupations (33)
     20. Food preparation and serving related occupations (35)
     21. Building and grounds cleaning and maintenance occupations (37)
     22. Personal care and service occupations (39)
     23. Social work occupations (21)

 Produce results in JSON format. Please do not include any other information in the JSON output.

 Example interactions:
 Query: "SR. SITE RELIABILITY ENGINEERING MANAGER"
 {
    "query_type": "job_title_query",
    "soc_major_group": "15-Architecture and engineering occupations",
    "soc_code": "15-1131",
    "soc_title": "Architects, Except Landscape and Naval"
 }

 Query: "Mcdonalds"
 {
    "query_type": "company_name_query",
    "soc_major_group": "",
    "soc_code": "",
    "soc_title": ""
 }

 Query: "CSX"
{
    "query_type": "company_name_query",
    "soc_major_group": "",
    "soc_code": "",
    "soc_title": ""
 }
 
 Query: "warehouse operator"
 {
    "query_type": "job_title_query",
    "soc_major_group": "41-Production occupations",
    "soc_code": "41-2011",
    "soc_title": "Production Worker"
 }
 
 Query: "delivery driver"
 {
    "query_type": "job_title_query",
    "soc_major_group": "43-Transportation and material moving occupations",
    "soc_code": "43-5011",
    "soc_title": "Driver/Sales Worker"
 }
 
 Query: "full stack developer"
 {
    "query_type": "job_title_query",
    "soc_major_group": "15-Architecture and engineering occupations",
    "soc_code": "15-1131",
    "soc_title": "Architects, Except Landscape and Naval"
 }
  
 Query: "part time"
 {
    "query_type": "job_type_query",
    "soc_major_group": "",
    "soc_code": "",
    "soc_title": ""
 }  
'''

def run_llm_judge_on_query(query, llms_as_judge):
    
    message = QUERY_ANALYSIS_PROMPT + "Query: " + query
    parsed = None    
    try:
        if llms_as_judge == LLMS_AS_JUDGE.LLAMA_3_3_70B:
            model_response = get_llama_to_judge(llms_as_judge, message)
            model_response = json.loads(repair_json(model_response))
            parsed = model_response["generation"]              
    except ClientError:
        logger.error("get_llama_to_judge.")
    return parsed

def run_llm_judge(result_position, llms_as_judge, query, location, job, PpcU, radius):

    few_shot_examples = get_few_shot_examples()
    raw_prompt = get_raw_prompt(few_shot_examples)
    message=raw_prompt.format(query=query, location=location, job=job, radius=radius)
    relevance_response = None
    
    try:
        if llms_as_judge == LLMS_AS_JUDGE.LLAMA_3_3_70B:
            model_response = get_llama_to_judge(llms_as_judge, message)
            model_response = json.loads(repair_json(model_response))
            try:
                generation = model_response["generation"]
                temp = json.loads(repair_json(generation))
                relevance = temp['Relevant']
                reason = temp['Reasoning']
                relevance_response = {"Relevancy": string_to_bool(relevance), "Reasoning": reason}
                return relevance_response
               # print(f"Rank: {result_position} PpcU: {PpcU} Query: {query} Location: {location} Job: {job} \nRelevant: {relevance} Reason: {reason}")                
            except:
                print("Error parsing model_response", model_response)
                return relevance_response                 
    except ClientError:
        logger.error("get_llama_to_judge.")
    return relevance_response
    
SEARCH_RELEVANCE_RATER_PROMPT = '''
    You are a job search expert with deep knowledge of the USA job market.
    You are responsible for validating and grading whether the retrieved job is relevant 
    to the query. Analyze and classify the query into categories like job title or employer name and use that information in the relevance grading.
    For a given input, you need to output a single token: "Yes" or "No" indicating the retrieved job title is semantically relevant to 
    the query string which is case insensitive. Also, provide one sentence reasoning for why it is relevant.
    
    Please return the output in JSON format. Please do not include any other information in the JSON output.
    
    Example interactions:
    Query: "SR. SITE RELIABILITY ENGINEERING MANAGER"
    Location: "san francisco"
    Job: "Sr. Site Reliability Engineer at Google in San Francisco,California,us"
    Relevant: Yes
    Reasoning: "The job title matches the query and is located in San Francisco."
    
    Query: Service Deli Clerk
    Location: San Francisco,us
    Job: """Service Deli Clerk at Costco Wholesale Corp. in San Francisco,California,us """
    Relevant: Yes
    Reasoning: "The job title matches the query and is located in San Francisco."
    
    Query: Service Deli Clerk
    Location: San Francisco,us
    Job: """Sales Clerk INT 5940 at Coast Guard Community Services Command in Alameda,California,us"""
    Relevant: No
    Reasoning: "The job title does not match the query."
    
    Query: Multimedia Journalist
    Location: San Antonio, TX 78247, USA
    Job: """*NEW* Virtual Speech Language Pathologist at Soliant in San Antonio,Texas,us"""
    Relevant: No
    Reasoning: "The job title does not match the query."

    Query: Multimedia Journalist
    Location: San Antonio, TX 78247, USA
    Job: """Program Manager at University Health in San Antonio,Texas,us"""
    Relevant: No
    Reasoning: "The job title does not match the query."

    Query: Service Deli Clerk
    Location: san francisco
    Job: Practice Management Clerk at Lewis Brisbois in San Francisco,California,us
    Relevant: No
    Reasoning: "The job title does not match the query."

    Query: Service Deli Clerk
    Location: san francisco
    Job: Deli Associate at Albertsons in Alameda,California,us
    Relevant:  Yes
    Reasoning: "The job title matches the query and is located in San Francisco."
'''    

    
def get_few_shot_examples():
    examples = '''
    Query: Service Deli Clerk
    Location: San Francisco,us
    Job: """Service Deli Clerk at Costco Wholesale Corp. in San Francisco,California,us """
    Relevant: Yes
    
    Query: Service Deli Clerk
    Location: San Francisco,us
    Job: """Sales Clerk INT 5940 at Coast Guard Community Services Command in Alameda,California,us"""
    Relevant: No
    
    Query: Multimedia Journalist
    Location: San Antonio, TX 78247, USA
    Job: """*NEW* Virtual Speech Language Pathologist at Soliant in San Antonio,Texas,us"""
    Relevant: No

    Query: Multimedia Journalist
    Location: San Antonio, TX 78247, USA
    Job: """Program Manager at University Health in San Antonio,Texas,us"""
    Relevant: No

    Query: Service Deli Clerk
    Location: san francisco
    Job: Practice Management Clerk at Lewis Brisbois in San Francisco,California,us
    Relevant: No

    Query: Service Deli Clerk
    Location: san francisco
    Job: Deli Associate at Albertsons in Alameda,California,us
    Relevant:  Yes
    '''
    return examples


def get_raw_prompt(few_shot_examples):
    raw_prompt = '''
    You are a job search expert with deep knowledge of the USA job market.
    You are responsible for validating and grading whether the retrieved job is relevant 
    to the query. Analyze and classify the query into categories like job title or employer name and use that information in the relevance grading.
    For a given input, you need to output a single token: "Yes" or "No" indicating the retrieved job title is semantically relevant to 
    the query string which is case insensitive. Also, provide one sentence reasoning for why it is relevant.
    Please return the output in JSON format.
    '''
    
    raw_question = '''
    Query: {query}
    Location: {location}
    Job: """{job}"""
    Relevant:
    '''
    combined_raw_prompt = raw_prompt + few_shot_examples + raw_question
    return combined_raw_prompt

def string_to_bool(s):    
    s = s.lower().strip()
    if s == "yes":
        return True
    elif s == "no":
        return False
    else:
        raise ValueError("Invalid input: must be 'yes' or 'no'")

def convert_html_to_json_headers(response_html):
    html = response_html
    soup = BeautifulSoup(html, 'html.parser')
    table = soup.find('table')
    if table is None:
        return None
    headers = [th.text for th in table.find_all('th')]  
    data = {}  
    
    for tr in table.find_all('tr')[1:]:   
        tds = tr.find_all('td')
        row_id = tds[0].text  # assuming the id is always in the first td (index 0)
        
        if row_id not in data:  
            data[row_id] = {}  # Initialize a dictionary for each unique ID
            
        for i, td in enumerate(tds):
            column_name = headers[i]
            
            # If the key already exists, append the new value to it as a list
            if column_name in data[row_id]:  
                data[row_id][column_name].append(td.text)
            else:  # otherwise initialize a new list with this value
                data[row_id][column_name] = [td.text]
            
    response_json = json.dumps(data, indent=4)  # Pretty-printing with 4 spaces per level of indentation
    return response_json

def call_search_playground(keyword,location,country,radius,min_ppcu,score_thres,retriever):
    # First curl command
    response = requests.get(
        url=PLAYGROUND_URL.SEARCH_SERVICE.value,
        params={
            'keyword': keyword, 
            'location': location, 
            'country': country, 
            'radius': radius, 
            'min_ppcu': min_ppcu, 
            'score_threshold': score_thres, 
            'retriever': retriever
        },
        headers={
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Chromium";v="134",  "Not:A-Brand";v="24", "Google Chrome";v="134"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"'
        }
    )
    #print(response.content)  # This will print the content of the response
    response_json=convert_html_to_json_headers(response.content)
    
    return response_json

def calculate_average_precision_at_k(results, max_k):
    k=0
    precision_sum = 0.0
    hits=0
    for result in results:
        hits += int(result[1])
        precision_sum += hits * float(1.0 / float(k+1))
        k += 1
        if k >= max_k:
            break
    average_precision = (precision_sum / float(k)) * 100.0
    return average_precision



if __name__ == "__main__":
    #list_foundation_models()

    radius='15'
    min_ppcu='0'
    score_thres='0.0001'
    retriever='semsearch_me5strainedtitledesc'
    # Example usage:
    queries = load_playground_queries('/Users/<USER>/talent/sandbox/scripts/semantic_search/playground_queries.csv')    
    max_queries = 50
    q = 0
    print("Running Relevancy test on {} queries using retriever: {}".format(max_queries, retriever))
    for query in queries:
        payload = call_search_playground(
            keyword=query['keyword'],
            location=query['location'],
            country=query['country'],
            radius=radius,
            min_ppcu=min_ppcu,
            score_thres=score_thres,
            retriever=retriever
        )
        # Process payload...
        k=0
        max_k = 10
        results = []            
        if payload is None:
            print("\nQuery: {}, hits: {}, AP@{}: {:.2f}".format(query, 0, k, 0.0))
            exit(0)
        dict_data = json.loads(payload)
        hits = len(dict_data)
        max_k = min(hits, max_k)    
        for data in dict_data:
            job = dict_data[data]['Title'][0] + " at " + dict_data[data]['Company Name'][0] + " in " + dict_data[data]['Location'][0]
            PpcU = dict_data[data]['PpcU'][0]
            relevance_response = run_llm_judge((k+1),LLMS_AS_JUDGE.LLAMA_3_3_70B, query['keyword'],query['location'],job, PpcU, radius='15')
            if relevance_response == FileNotFoundError:
                print("Error getting relevancy for job: ", job)
                continue
            job_id = dict_data[data]['ID'][0]        
            results.append((job_id, relevance_response["relevancy"]))
            k += 1
            if k >= max_k:
                print("dine")
                break
            
        print("Query: {}, hits: {}, AP@{}: {:.2f}".format(query, hits, k, calculate_average_precision_at_k(results, k)))    
        q += 1
        if q >= max_queries:
            print("Done.")
            break  

    exit(0)
   



