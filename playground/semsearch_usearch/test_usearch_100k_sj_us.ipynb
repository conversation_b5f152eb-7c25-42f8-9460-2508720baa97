import json
import argparse
from typing import List, Dict, Any
from gliner import GLiNER
from sentence_transformers import SentenceTransformer
import numpy as np
from usearch.index import Index, Matches
from json_repair import repair_json
import traceback, sys
from tqdm import tqdm
from pprint import pprint
from geopy.geocoders import Nominatim
import random


def read_jobs(file_path: str) -> List[Dict[str, Any]]:
    """
    Read a JSONL file and return a list of parsed JSON documents.
    
    Args:
        file_path (str): Path to the JSONL file
        
    Returns:
        List[Dict[str, Any]]: List of parsed JSON documents
    """
    jobs = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_number, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue
                line = line.replace('""', '"')
                line = line.replace('"{', '{')
                line = line.replace('}"', '}')
                try:                    
                    job = json.loads(repair_json(line))
                    if (isinstance(job, list)):
                        job = job[0]
                        
                    try:
                        id = int(job['value']['id'])
                    except:
                        print(job)
                    id = int(job['value']['id'])
                    jobs[id] = job
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON at line {line_number}: {e}")
                    print(f"Line content: {line}...")  # Print first 100 chars of problematic line
    except FileNotFoundError:
        print(f"File not found: {file_path}")
    except Exception as e:
        # Print the exception type and message
        print(f"Exception: {type(e).__name__} - {e}")

        # Print the stack trace using traceback.print_exc()
        print("Stack Trace:")
        traceback.print_exc(file=sys.stdout)
        
    return jobs

file_path="100K_jobs_json.txt"
jobs = read_jobs(file_path)


for job_id, job in jobs.items():    
    pprint(job)
    break

def write_jobs(jobs, file_path):
    with open(file_path, 'w') as f:
        for job_id, job in tqdm(jobs.items(), desc="Writing jobs to file", total=len(jobs)):
            source_title = job['value']['source_title']            
            if 'source_company_name' in job['value']:
                source_company_name = job['value']['source_company_name']
            else:
                source_company_name = ""         
            source_location = job['value']['source_location']
            f.write("Job ID: " + str(job_id) + "\n")
            f.write("Job Title: " + source_title + "\n")
            f.write("job Location: " + source_location + "\n")
            f.write("Company Name: " + source_company_name + "\n")
            f.write("\n")
            
            

def write_jobs_one_per_file(jobs, file_path):
    for job_id, job in tqdm(jobs.items(), desc="Writing jobs to file", total=len(jobs)):
        file_path_job = file_path + "/" + str(job_id) + ".txt"
        with open(file_path_job, 'w') as f:
            source_title = job['value']['source_title']            
            if 'source_company_name' in job['value']:
                source_company_name = job['value']['source_company_name']
            else:
                source_company_name = ""         
            source_location = job['value']['source_location']
            f.write("Job ID: " + str(job_id) + "\n")
            f.write("Job Title: " + source_title + "\n")
            f.write("job Location: " + source_location + "\n")
            f.write("Company Name: " + source_company_name + "\n")
            f.write("\n")
        

import numpy as np 
def ellipsoid_to_cartesian_deg(lat_deg, lon_deg, a=6378137.0, b=6356752.314245):   
   lon = np.radians(lon_deg)
   lat = np.radians(lat_deg)
   x = a * np.cos(lat) * np.cos(lon)
   y = a * np.cos(lat) * np.sin(lon)
   z = b * np.sin(lat)
   return np.array([x, y, z])

NUM_EMB_DIM=384

#gliner_model = GLiNER.from_pretrained("urchade/gliner_multi-v2.1")
def get_distilled_job_title_extractor(gliner_model, jobs):
    job_titles = []
    for job_id, job in jobs.items():
        source_title = job['value']['source_title']
        source_location = job['value']['source_location']
        
        labels = ["job title", "job type", "salary"]
        entities = gliner_model.predict_entities(source_title, labels)
        job_title = ""
        for entity in entities:
            job_title = entity["text"]
        #print(f"Input: {source_title}, Job Title: {job_title}")
        job_title = source_title if job_title == "" else job_title      
        job_titles.append(job_title)
    return job_titles

def get_job_embeddings(embedding_model, jobs, max_count=1000000):    
    rows, cols = max_count, NUM_EMB_DIM    
    job_embeddings = np.zeros((rows, cols))
    id_map = {}
    count = 0
    for job_id, job in tqdm(jobs.items()):
        job_title = job['value']['source_title']
        source_location = job['value']['source_location']
        if job['value']['source_company_name'] is None:
            employer_name = ""
        else:
            employer_name = job['value']['source_company_name']
        #job_title = get_distilled_job_title_extractor(gliner_model, job_title)
        passage = "passage: " + job_title + " " + employer_name
        job_embedding = embedding_model.encode(passage, normalize_embeddings=True)
        job_embeddings[count] = job_embedding
        #print(job_id)
        id_map[count] = job_id
        count += 1
        if count >= max_count:
            break
    return id_map, job_embeddings

def get_query_embedding(embedding_model, query):
    query = "query: " + query
    query_embedding = embedding_model.encode(query, normalize_embeddings=True)
    return query_embedding

#job_titles = get_distilled_job_title_extractor(gliner_model, jobs)

def create_embeddings_for_jobs(embedding_model, jobs, max_count=1000):    
    id_map, job_embeddings = get_job_embeddings(embedding_model, jobs, max_count)
    return id_map, job_embeddings    

def create_vector_index(jobs, id_map, job_embeddings):

    index = Index(
        ndim=NUM_EMB_DIM, # Define the number of dimensions in input vectors
        metric='cos', # Choose 'l2sq', 'haversine' or other metric, default = 'ip'
        dtype='f32', # Quantize to 'f16' or 'i8' if needed, default = 'f32'
        connectivity=16, # How frequent should the connections in the graph be, optional
        expansion_add=128, # Control the recall of indexing, optional
        expansion_search=64, # Control the quality of search, optional
    )
    keys = np.arange(len(id_map))
    indexed_keys = index.add(keys, job_embeddings)
    return index

def run_search(embedding_model, index, jobs, id_map, search_query = 'TRAVEL LPN'):    
    query_embedding = get_query_embedding(embedding_model, search_query)
    vector = np.array(query_embedding)
    matches: Matches = index.search(vector, 10)
    for match in matches:
        job_id = id_map[match.key]
        print(f"ID: {match.key}, job_title: {jobs[job_id]['value']['source_title']}, Distance: {match.distance}")

file_path="100K_jobs_json.txt"
jobs = read_jobs(file_path)
embedding_model = SentenceTransformer('intfloat/multilingual-e5-small')
id_map, job_embeddings = create_embeddings_for_jobs(embedding_model, jobs, max_count = 1000)
vector_index = create_vector_index(jobs, id_map, job_embeddings)
run_search(embedding_model, vector_index, jobs, id_map, search_query="software engineer")

max_count = 100000
total_count = min(max_count, len(jobs.items()))
count = 0
geo_vectors = np.zeros((total_count, 2))
id_map = {}
for job_id, job in tqdm(jobs.items()):
    #pprint(job['value'])
    geo_vector = np.array([job['value']['enrich_geo_point'][1], job['value']['enrich_geo_point'][0]])
    #print(geo_vector)
    geo_vectors[count] = geo_vector
    id_map[count] = job_id
    count += 1
    if count >= total_count:
        break
print(geo_vectors.shape)

geo_index = Index(
    ndim=2, # Define the number of dimensions in input vectors
    metric='haversine', # Choose 'l2sq', 'haversine' or other metric, default = 'ip'
    dtype='f32', # Quantize to 'f16' or 'i8' if needed, default = 'f32'
)
keys = np.arange(len(id_map))
geo_indexed_keys = geo_index.add(keys, geo_vectors)





random_key = random.choice(list(id_map.values()))
random_job = jobs[random_key]
pprint(f"Query: {random_job}")
query_geo = np.array([random_job['value']['enrich_geo_point'][1], random_job['value']['enrich_geo_point'][0]])
print(query_geo)
query_geo_vector = np.zeros((1,2))
query_geo_vector[0] = query_geo
print(query_geo_vector.shape)
matches: Matches = geo_index.search(query_geo_vector, 10)
id_list_1 = []
for match in matches:
    job_id = id_map[match.key]
    id_list_1.append(job_id)
    print(f"ID: {job_id}, job_title: {jobs[job_id]['value']['source_title']}, location: {jobs[job_id]['value']['source_location']}, geo: {jobs[job_id]['value']['enrich_geo_point']}, Distance: {match.distance}")



max_count = 100000
total_count = min(max_count, len(jobs.items()))
count = 0
geo_vectors = np.zeros((total_count, 3))
id_map = {}
for job_id, job in tqdm(jobs.items()):
    #pprint(job['value'])
    #geo_vector = np.array([job['value']['enrich_geo_point'][1], job['value']['enrich_geo_point'][0]])
    geo_vector = ellipsoid_to_cartesian_deg(job['value']['enrich_geo_point'][1], job['value']['enrich_geo_point'][0])
    #print(geo_vector)
    geo_vectors[count] = geo_vector
    id_map[count] = job_id
    count += 1
    if count >= total_count:
        break
print(geo_vectors.shape)

geo_index_car = Index(
    ndim=3, # Define the number of dimensions in input vectors
    metric='haversine', # Choose 'l2sq', 'haversine' or other metric, default = 'ip'
    dtype='f32', # Quantize to 'f16' or 'i8' if needed, default = 'f32'
)
keys = np.arange(len(id_map))
geo_indexed_keys = geo_index_car.add(keys, geo_vectors)

pprint(f"Query: {random_job}")
#query_geo = np.array([random_job['value']['enrich_geo_point'][1], random_job['value']['enrich_geo_point'][0]])
query_geo = ellipsoid_to_cartesian_deg(random_job['value']['enrich_geo_point'][1], random_job['value']['enrich_geo_point'][0])
print(query_geo)
query_geo_vector = np.zeros((1,3))
query_geo_vector[0] = query_geo
print(query_geo_vector.shape)
matches_car: Matches = geo_index_car.search(query_geo_vector, 10)
id_list_2 = []
for match in matches_car:
    job_id = id_map[match.key]
    id_list_2.append(job_id)
    print(f"ID: {job_id}, job_title: {jobs[job_id]['value']['source_title']}, location: {jobs[job_id]['value']['source_location']}, geo: {jobs[job_id]['value']['enrich_geo_point']}, Distance: {match.distance}")

def jaccard_similarity(list1, list2):
    """
    Calculate the Jaccard similarity between two lists of IDs.
    
    Jaccard similarity is defined as the size of the intersection divided by the size of the union
    of the two sets.
    
    Args:
        list1 (list): First list of IDs
        list2 (list): Second list of IDs
        
    Returns:
        float: Jaccard similarity coefficient between 0 and 1
              - 0 means no overlap (completely different)
              - 1 means identical sets
    """
    # Convert lists to sets for efficient intersection and union operations
    set1 = set(list1)
    set2 = set(list2)
    
    # Calculate intersection and union
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    # Handle edge case of empty sets
    if union == 0:
        return 0.0
    
    # Return Jaccard similarity coefficient
    return intersection / union


def jaccard_distance(list1, list2):
    """
    Calculate the Jaccard distance between two lists of IDs.
    
    Jaccard distance is defined as 1 - Jaccard similarity.
    
    Args:
        list1 (list): First list of IDs
        list2 (list): Second list of IDs
        
    Returns:
        float: Jaccard distance between 0 and 1
              - 0 means identical sets
              - 1 means no overlap (completely different)
    """
    return 1.0 - jaccard_similarity(list1, list2)

print(jaccard_similarity(id_list_1, id_list_2))


print(id_list_1)

print(id_list_2)

geolocator = Nominatim(user_agent="usearch")
location = geolocator.geocode("1600 Amphitheatre Parkway, Mountain View, CA")
try:
    
    if location:
        print(f"Location: {location.address}")
        print(f"Latitude: {location.latitude}, Longitude: {location.longitude}")
    else:
        print("Location not found")
except Exception as e:
    print(f"An error occurred: {e}")

