import json
import argparse
from typing import List, Dict, Any
from gliner import GLi<PERSON><PERSON>
from sentence_transformers import SentenceTransformer
import numpy as np
from usearch.index import Index, Matches
from usearch.index import kmeans
from usearch.io import load_matrix
from json_repair import repair_json
import traceback, sys
from tqdm import tqdm
from pprint import pprint
import random, os
import semantic_job_search_batched


# Constants
NUM_EMB_DIM = 384
DEFAULT_MODEL = 'intfloat/multilingual-e5-small'
#DEFAULT_MODEL='/Users/<USER>/Downloads/tei_two_tower'
DEFAULT_BATCH_SIZE = 1000
MAX_JOB_TITLE_LENGTH=30
MAX_ITERS=1000
K = 25
jobs_file="100K_jobs_json.txt"
max_jobs=10000
index_file='jobs_index.usearch'

# Load the embedding model
print(f"Loading embedding model: {DEFAULT_MODEL}")
embedding_model = SentenceTransformer(DEFAULT_MODEL)


# Check if index exists and should be loaded
index_exists = False
id_map_file = f"{os.path.splitext(index_file)[0]}_id_map.json"
id_map_exists = os.path.exists(id_map_file)

# Process jobs in batches and build the index
print(f"Processing jobs in batches of {DEFAULT_BATCH_SIZE}")
id_map, jobs, job_embeddings = semantic_job_search_batched.process_batches_to_embeddings(
    embedding_model, 
    jobs_file,
    max_jobs, 
    DEFAULT_BATCH_SIZE
)

def cluster_with_usearch(X, k, max_iters=100):
    assignments, _, centroids = kmeans(X, k, max_iterations=max_iters)
    return assignments, centroids


labels, centroids = cluster_with_usearch(job_embeddings, K, max_iters=MAX_ITERS)

for job_id, job in tqdm(jobs.items()):
    pprint(job)
    break


# Visualize the clusters
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import matplotlib.cm as cm
from matplotlib.colors import Normalize
from adjustText import adjust_text

def visualize_job_clusters(embeddings, labels, job_ids=None, method='tsne', max_labels=50, figsize=(20, 16)):
    """
    Visualize job clusters in 2D space with job IDs as labels.
    
    Args:
        embeddings: Job embeddings
        labels: Cluster assignments
        job_ids: List of job IDs (optional)
        method: Dimension reduction method ('pca' or 'tsne')
        max_labels: Maximum number of labels to show
        figsize: Figure size
    """
    # Reduce dimensions for visualization
    print(f"Reducing dimensions using {method}...")
    if method == 'pca':
        reducer = PCA(n_components=2, random_state=42)
        embeddings_2d = reducer.fit_transform(embeddings)
    else:  # tsne
        # First reduce with PCA to 50 dimensions for speed if embeddings are large
        if embeddings.shape[1] > 50:
            print("Pre-reducing with PCA to 50 dimensions...")
            pca = PCA(n_components=50, random_state=42)
            embeddings_pca = pca.fit_transform(embeddings)
        else:
            embeddings_pca = embeddings
            
        reducer = TSNE(n_components=2, perplexity=30, max_iter=1000, random_state=42)
        embeddings_2d = reducer.fit_transform(embeddings_pca)
    
    # Set up the plot
    plt.figure(figsize=figsize)
    
    # Get unique clusters and assign colors
    unique_clusters = np.unique(labels)
    num_clusters = len(unique_clusters)
    cmap = cm.get_cmap('rainbow', num_clusters)
    norm = Normalize(vmin=min(unique_clusters), vmax=max(unique_clusters))
    
    # Plot each point
    scatter = plt.scatter(
        embeddings_2d[:, 0], 
        embeddings_2d[:, 1], 
        c=labels, 
        cmap=cmap, 
        norm=norm,
        alpha=0.7, 
        s=50
    )
    
    # Add a colorbar
    cbar = plt.colorbar(scatter)
    #cbar.set_label('Cluster ID', fontsize=14)
    
    # Add cluster centroids
    centroids = np.zeros((num_clusters, 2))
    for i, cluster_id in enumerate(unique_clusters):
        mask = labels == cluster_id
        centroids[i] = np.mean(embeddings_2d[mask], axis=0)
        
    plt.scatter(
        centroids[:, 0], 
        centroids[:, 1], 
        marker='X', 
        s=200, 
        c=range(num_clusters), 
        cmap=cmap, 
        norm=norm,
        edgecolors='black', 
        linewidths=2
    )
    
    # Add cluster labels at centroids
    # for i, cluster_id in enumerate(unique_clusters):
    #     mask = labels == cluster_id
    #     jobs_in_cluster = [job_ids[j] for j in np.where(mask)[0]]
    #     plt.annotate(
    #         f"{jobs_in_cluster[1]}",
    #         (centroids[i, 0], centroids[i, 1]),
    #         fontsize=8,
    #         fontweight='bold',
    #         ha='center',
    #         va='center',
    #         bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="black", alpha=0.8)
    #     )
    
    # Add job ID labels for a subset of points
    texts = []
    if max_labels > 0 and job_ids is not None:
        # Sample points from each cluster to label
        indices_to_label = []
        for cluster_id in unique_clusters:
            cluster_indices = np.where(labels == cluster_id)[0]
            # Take a few samples from each cluster
            samples_per_cluster = max(1, max_labels // num_clusters)
            if len(cluster_indices) > samples_per_cluster:
                cluster_indices = np.random.choice(cluster_indices, samples_per_cluster, replace=False)
            indices_to_label.extend(cluster_indices)
        
        # Ensure we don't exceed max_labels
        if len(indices_to_label) > max_labels:
            indices_to_label = np.random.choice(indices_to_label, max_labels, replace=False)
        
        # Add text labels
        for idx in indices_to_label:
            job_id = job_ids[idx] if idx < len(job_ids) else f"Job-{idx}"
            texts.append(plt.text(
                embeddings_2d[idx, 0], 
                embeddings_2d[idx, 1], 
                str(job_id),
                fontsize=9, 
                fontweight='bold'
            ))
    
    # Adjust text positions to minimize overlap
    if texts:
        print("Adjusting text positions to minimize overlap...")
        try:
            adjust_text(texts, arrowprops=dict(arrowstyle='->', color='black', lw=0.5))
        except:
            print("Warning: Could not adjust text positions. Install adjustText package with: pip install adjustText")
    
    # Set plot title and labels
    plt.title('100K US Job Clusters Visualization', fontsize=20)
    plt.xlabel('Semantic Dimension 1', fontsize=14)
    plt.ylabel('Semantic Dimension 2', fontsize=14)
    plt.tight_layout()
    
    return plt

def plot_cluster_sizes(labels):
    """Create a bar chart showing the size of each cluster."""
    # Count jobs in each cluster
    unique_clusters, counts = np.unique(labels, return_counts=True)
    
    # Sort by cluster size
    sorted_indices = np.argsort(counts)[::-1]
    unique_clusters = unique_clusters[sorted_indices]
    counts = counts[sorted_indices]
    
    # Create the plot
    plt.figure(figsize=(15, 8))
    bars = plt.bar(range(len(unique_clusters)), counts, color=cm.rainbow(np.linspace(0, 1, len(unique_clusters))))
    
    # Add labels
    plt.xlabel('Cluster ID', fontsize=14)
    plt.ylabel('Number of Jobs', fontsize=14)
    plt.title('Cluster Sizes', fontsize=20)
    plt.xticks(range(len(unique_clusters)), [str(c) for c in unique_clusters], rotation=45)
    
    # Add count labels on top of bars
    for bar, count in zip(bars, counts):
        plt.text(
            bar.get_x() + bar.get_width()/2,
            bar.get_height() + 5,
            str(count),
            ha='center',
            fontsize=10
        )
    
    plt.tight_layout()
    return plt

# Generate job IDs for visualization (replace with actual job IDs if available)
#job_ids = [f"Job-{i}" for i in range(len(job_embeddings))]
job_ids = []
for index, job_id in tqdm(id_map.items()):
    job_ids.append(jobs[job_id]['value']['source_title'][:MAX_JOB_TITLE_LENGTH])

# Visualize the clusters
print("Visualizing job clusters...")
plt_clusters = visualize_job_clusters(job_embeddings, labels, job_ids=job_ids)
plt_clusters.savefig('job_clusters_visualization.png', dpi=500, bbox_inches='tight')
plt_clusters.show()

# Plot cluster sizes
print("Plotting cluster sizes...")
plt_sizes = plot_cluster_sizes(labels)
plt_sizes.savefig('cluster_sizes.png', dpi=300, bbox_inches='tight')
plt_sizes.show()




def evaluate_clustering_euclidean(X, labels, centroids):
    """Evaluate clustering quality as average distance to centroids"""
    distances = np.linalg.norm(X - centroids[labels], axis=1)
    return np.mean(distances)


def evaluate_clustering_cosine(X, labels, centroids):
    """Evaluate clustering quality as average cosine distance to centroids"""

    # Normalize both data points and centroids
    X_normalized = X / np.linalg.norm(X, axis=1, keepdims=True)
    centroids_normalized = centroids / np.linalg.norm(centroids, axis=1, keepdims=True)

    # Compute cosine similarity using dot product
    cosine_similarities = np.sum(X_normalized * centroids_normalized[labels], axis=1)

    # Convert cosine similarity to cosine distance
    cosine_distances = 1 - cosine_similarities

    # Return the average cosine distance
    return np.mean(cosine_distances)

# Analyze cluster quality
print("\nCluster Quality Metrics:")
print(f"Average Euclidean distance to centroids: {evaluate_clustering_euclidean(job_embeddings, labels, centroids):.4f}")
print(f"Average Cosine distance to centroids: {evaluate_clustering_cosine(job_embeddings, labels, centroids):.4f}")
# Let's compare it to some random uniform assignment
random_labels = np.random.randint(0, K, size=job_embeddings.shape[0])
random_quality = evaluate_clustering_euclidean(job_embeddings, random_labels, centroids)
random_quality_cosine = evaluate_clustering_cosine(job_embeddings, random_labels, centroids)
print(f"Random assignment quality (Euclidean): {random_quality:.4f}")
print(f"Random assignment quality (Cosine): {random_quality_cosine:.4f}")

# Print the number of jobs in each cluster
unique_clusters, counts = np.unique(labels, return_counts=True)
print("\nCluster Sizes:")
for cluster_id, count in sorted(zip(unique_clusters, counts), key=lambda x: x[1], reverse=True):
    print(f"Cluster {cluster_id}: {count} jobs")

