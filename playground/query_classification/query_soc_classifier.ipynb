import json
import argparse
from typing import List, Dict, Any
from gliner import GLiNER
import numpy as np
from json_repair import repair_json
import traceback, sys
from tqdm import tqdm
from pprint import pprint
import random, os
import re, csv

sys.path.append(os.path.abspath('../utilities/'))
from athena_utility import AthenaUtility


# Initialize the utility
athena = AthenaUtility(
    region_name='us-east-1',
    s3_output_location='s3://sk-ml-ds/athena-results/',
    database='ml'
)

# Example 1: List databases and tables
print("Available databases:", athena.list_databases())
print("Tables in default database:", athena.list_tables())

params_query = """
SELECT * 
FROM "ml"."prints_clicks_crawledoutput" 
WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
AND length(keyword) > :min_keyword_length
AND has_click > :has_click
LIMIT :limit
"""

parameters = {
    'min_keyword_length': 1,
    'has_click': 0,
    'limit': 20000
}

results_df = athena.query_to_dataframe(params_query, parameters=parameters)

