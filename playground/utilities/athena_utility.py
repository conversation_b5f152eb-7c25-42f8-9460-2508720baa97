import boto3
import time
import pandas as pd
import os
from typing import Dict, List, Optional, Union, Any
import logging

class AthenaUtility:
    """Utility class for interacting with Amazon Athena."""
    
    def __init__(
        self, 
        region_name: str = 'us-east-1',
        s3_output_location: str = None,
        database: str = None,
        profile_name: Optional[str] = None
    ):
        """
        Initialize the Athena utility.
        
        Args:
            region_name: AWS region name
            s3_output_location: S3 location to store query results
            database: Default Athena database to use
            profile_name: AWS profile name to use (optional)
        """
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # Initialize AWS session
        session_kwargs = {'region_name': region_name}
        if profile_name:
            session_kwargs['profile_name'] = profile_name
            
        session = boto3.Session(**session_kwargs)
        self.athena_client = session.client('athena')
        
        self.s3_output_location = s3_output_location
        self.database = database
        
    def execute_query(
        self, 
        query: str, 
        database: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        wait: bool = True,
        max_execution_time: int = 300
    ) -> Dict:
        """
        Execute a query on Athena.
        
        Args:
            query: SQL query to execute
            database: Database to use (overrides default)
            parameters: Dictionary of parameters to substitute in the query
            wait: Whether to wait for query completion
            max_execution_time: Maximum time to wait for query completion (seconds)
            
        Returns:
            Dictionary with query execution details
        """
        # Apply parameters to query if provided
        if parameters:
            for key, value in parameters.items():
                # Handle different types of parameters
                if isinstance(value, str):
                    # Escape single quotes in string parameters
                    value = value.replace("'", "''")
                    value = f"'{value}'"
                elif isinstance(value, (list, tuple)):
                    # Format lists as comma-separated strings with quotes
                    value = ", ".join([f"'{item}'" if isinstance(item, str) else str(item) for item in value])
                    value = f"({value})"
                
                # Replace parameter in query
                placeholder = f":{key}"
                query = query.replace(placeholder, str(value))
        
        self.logger.info(f"Executing query: {query[:100]}...")
        
        # Start query execution
        response = self.athena_client.start_query_execution(
            QueryString=query,
            QueryExecutionContext={
                'Database': database or self.database
            },
            ResultConfiguration={
                'OutputLocation': self.s3_output_location
            }
        )
        
        query_execution_id = response['QueryExecutionId']
        self.logger.info(f"Query execution ID: {query_execution_id}")
        
        if wait:
            # Wait for query to complete
            state = 'RUNNING'
            start_time = time.time()
            
            while state in ['RUNNING', 'QUEUED'] and (time.time() - start_time) < max_execution_time:
                response = self.athena_client.get_query_execution(QueryExecutionId=query_execution_id)
                state = response['QueryExecution']['Status']['State']
                
                if state in ['RUNNING', 'QUEUED']:
                    time.sleep(2)  # Wait before checking again
                    
            if state == 'SUCCEEDED':
                self.logger.info("Query executed successfully")
                return response
            else:
                error_message = response['QueryExecution']['Status'].get('StateChangeReason', 'Unknown error')
                self.logger.error(f"Query failed: {error_message}")
                raise Exception(f"Query failed: {error_message}")
        
        return {'QueryExecutionId': query_execution_id}
    
    def get_query_results(
        self, 
        query_execution_id: str,
        max_results: int = 1000
    ) -> pd.DataFrame:
        """
        Get the results of a query as a pandas DataFrame.
        
        Args:
            query_execution_id: Query execution ID
            max_results: Maximum number of results to return
            
        Returns:
            pandas DataFrame with query results
        """
        results = []
        next_token = None
        column_names = []
        
        # Paginate through results
        while True:
            kwargs = {
                'QueryExecutionId': query_execution_id,
                'MaxResults': max_results
            }
            
            if next_token:
                kwargs['NextToken'] = next_token
                
            response = self.athena_client.get_query_results(**kwargs)
            
            # Extract column names from first page
            if not column_names and response['ResultSet']['Rows']:
                column_names = [col['VarCharValue'] for col in response['ResultSet']['Rows'][0]['Data']]
                # Skip the header row for data
                data_rows = response['ResultSet']['Rows'][1:]
            else:
                data_rows = response['ResultSet']['Rows']
            
            # Extract data
            for row in data_rows:
                results.append([col.get('VarCharValue', None) for col in row['Data']])
            
            # Check if there are more results
            next_token = response.get('NextToken')
            if not next_token:
                break
        
        # Create DataFrame
        df = pd.DataFrame(results, columns=column_names)
        return df
    
    def query_to_dataframe(
        self, 
        query: str, 
        database: Optional[str] = None,
        parameters: Optional[Dict[str, Any]] = None,
        max_results: int = 1000
    ) -> pd.DataFrame:
        """
        Execute a query and return results as a pandas DataFrame.
        
        Args:
            query: SQL query to execute
            database: Database to use (overrides default)
            parameters: Dictionary of parameters to substitute in the query
            max_results: Maximum number of results to return
            
        Returns:
            pandas DataFrame with query results
        """
        response = self.execute_query(query, database, parameters)
        query_execution_id = response['QueryExecution']['QueryExecutionId']
        return self.get_query_results(query_execution_id, max_results)
    
    def list_databases(self) -> List[str]:
        """
        List all databases in Athena.
        
        Returns:
            List of database names
        """
        response = self.athena_client.list_databases(
            CatalogName='AwsDataCatalog'
        )
        return [db['Name'] for db in response['DatabaseList']]
    
    def list_tables(self, database: Optional[str] = None) -> List[str]:
        """
        List all tables in a database.
        
        Args:
            database: Database name (uses default if not specified)
            
        Returns:
            List of table names
        """
        response = self.athena_client.list_table_metadata(
            CatalogName='AwsDataCatalog',
            DatabaseName=database or self.database
        )
        return [table['Name'] for table in response['TableMetadataList']]
    
    def get_table_schema(self, table_name: str, database: Optional[str] = None) -> Dict:
        """
        Get the schema of a table.
        
        Args:
            table_name: Name of the table
            database: Database name (uses default if not specified)
            
        Returns:
            Dictionary with table schema information
        """
        response = self.athena_client.get_table_metadata(
            CatalogName='AwsDataCatalog',
            DatabaseName=database or self.database,
            TableName=table_name
        )
        return response['TableMetadata']
    
    def save_to_csv(self, df: pd.DataFrame, output_path: str) -> None:
        """
        Save a DataFrame to a CSV file.
        
        Args:
            df: pandas DataFrame to save
            output_path: Path to save the CSV file
        """
        df.to_csv(output_path, index=False)
        self.logger.info(f"Results saved to {output_path}")


# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Initialize the utility
    athena = AthenaUtility(
        region_name='us-east-1',
        s3_output_location='s3://sk-ml-ds/athena-results/',
        database='ml'
    )
    
    # Example 1: List databases and tables
    print("Available databases:", athena.list_databases())
    print("Tables in default database:", athena.list_tables())
    
    # Example 2: Run a simple query
    query = """SELECT *
    FROM "ml"."prints_clicks_crawledoutput"
    WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
    AND length(keyword) > 1
    LIMIT 10;"""


    df = athena.query_to_dataframe(query)
    print(df.head())
    
    # # Example 3: Run a parameterized query
    # params_query = """
    # SELECT * 
    # FROM your_table 
    # WHERE date_column >= :start_date 
    # AND date_column <= :end_date
    # AND category IN :categories
    # LIMIT :limit
    # """
    
    # parameters = {
    #     'start_date': '2023-01-01',
    #     'end_date': '2023-01-31',
    #     'categories': ['category1', 'category2', 'category3'],
    #     'limit': 100
    # }
    
    # results_df = athena.query_to_dataframe(params_query, parameters=parameters)
    
    # Save results to CSV
    athena.save_to_csv(results_df, 'query_results.csv')