import json
import argparse
from typing import List, Dict, Any
from gliner import GLiNER
from sentence_transformers import SentenceTransformer
import numpy as np
from usearch.index import Index, Matches
from usearch.index import kmeans
from usearch.io import load_matrix
from json_repair import repair_json
import traceback, sys
from tqdm import tqdm
from pprint import pprint
import random, os
from athena_utility import AthenaUtility
import re, csv

import sys
import os

# Add the directory containing search_playground_llm_rater.py to the Python path
sys.path.append(os.path.abspath('../search_relevance_llm_rater'))

# Import the module
from search_playground_llm_rater import run_llm_judge, LLMS_AS_JUDGE, get_few_shot_examples, get_raw_prompt, run_llm_judge_on_query

# Initialize the utility
athena = AthenaUtility(
    region_name='us-east-1',
    s3_output_location='s3://sk-ml-ds/athena-results/',
    database='ml'
)

# Example 1: List databases and tables
print("Available databases:", athena.list_databases())
print("Tables in default database:", athena.list_tables())

# query = """SELECT *
# FROM "ml"."prints_clicks_crawledoutput"
# WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
# AND length(keyword) > 1
# LIMIT 10;"""
# df = athena.query_to_dataframe(query)
# df.head()

params_query = """
SELECT * 
FROM "ml"."prints_clicks_crawledoutput" 
WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
AND length(keyword) > :min_keyword_length
AND has_click > :has_click
LIMIT :limit
"""

parameters = {
    'min_keyword_length': 1,
    'has_click': 0,
    'limit': 20000
}

results_df = athena.query_to_dataframe(params_query, parameters=parameters)





def parse_custom_format(text):
    #print(text)
    # Remove the outer brackets
    text = text.strip()[1:-1]    
    # Split by the pattern that indicates a new object
    items_str = re.split(r'}, \{', text)    
    # Process each item
    result = []
    for item_str in items_str:
        
        # Clean up the item string
        item_str = item_str.strip()
        if item_str.endswith('}'):
            item_str = item_str[:-1]
        if item_str.startswith('{'):
            item_str = item_str[1:]        
        # Split by comma and space to get key-value pairs
        pairs = item_str.split(', ')        
        # Create a dictionary for this item
        item_dict = {}
        for pair in pairs:
            if '=' in pair:
                key, value = pair.split('=', 1)
                # Convert value types where appropriate
                if value == 'null':
                    value = None
                elif value.isdigit():
                    value = int(value)
                elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                    value = float(value)
                elif value.lower() == 'true':
                    value = True
                elif value.lower() == 'false':
                    value = False
                item_dict[key] = value        
        result.append(item_dict)    
    return result

# # Parse the string
# job_data = parse_custom_format('[{bidresult_display=true, bidresult_exposure=100, bidresult_id=558589172630296121, bidresult_ppc=85, bidresult_ppc_u=1215, bidresult_bid=187, bidresult_cpc=62, bidresult_cpc_u=896, bidresult_bidshare=74, bidresult_system_job_hash=cec851fd751558fda918546e544dcd52, bidresult_display_reset_by_sonic=null, country=us, empname=TrackFive, id=558589172630296121, index=sonic, language=en, lat=44.5133188, lon=-88.0132958, original_ppc_u=0, query_source_engine=sonic, query_source_initiator= Admin, query_source_querytype=initial, score_ctr_prediction=0.0, score_final_score=1216.0, score_organic_score=0.0, score_original_score=0.0, shardid=2, soc_onetsoc_code=29-1141.00, title=Travel Nurse RN - Admin/Mgmt - $2,120 per week in Madison, WI}, {bidresult_display=true, bidresult_exposure=100, bidresult_id=538061504500278544, bidresult_ppc=38, bidresult_ppc_u=548, bidresult_bid=187, bidresult_cpc=30, bidresult_cpc_u=438, bidresult_bidshare=80, bidresult_system_job_hash=null, bidresult_display_reset_by_sonic=null, country=us, empname=GL Inc., id=538061504500278544, index=sonic, language=, lat=44.2619309, lon=-88.41538469999999, original_ppc_u=0, query_source_engine=sonic, query_source_initiator=data entry clerk, query_source_querytype=jobsense, score_ctr_prediction=0.0, score_final_score=549.0, score_organic_score=0.0, score_original_score=0.0, shardid=5, soc_onetsoc_code=43-9021.00, title=Work from Home Data Entry Clerk}]')

# # Print the first item to verify
# print(job_data[0])
# print(f"Total items: {len(job_data)}")

query_results_clicks_list = []

for row in tqdm(results_df.itertuples()):  
    search_request = json.loads(row.request)
    #pprint(search_request)
    search_id = row.search_id
    partner = search_request['config']['partner']
    location = search_request['geo']['formatted_address']
    keyword = search_request['keyword']
    radius = search_request['radius']
    if 'minppcu' in search_request['config']:
        min_ppcu = search_request['config']['minppcu']
    else:
        min_ppcu = 0
    jobs_printed = parse_custom_format(row.jobs)
    #pprint(jobs_printed)
    #break
    num_jobs_printed = len(jobs_printed)    
    #print(f"partner: {partner}, location: {location}, keyword: {keyword}, num_jobs_printed: {num_jobs_printed}")
    clicked_job_id = int(row.clicked_job_id)
    results = []
    position = 0
    for job in jobs_printed:
        job_id = int(job['id'])
        job_title = job['title']
        bidresult_ppc_u = job['bidresult_ppc_u']
        search_type = job['query_source_querytype']
        score_final_score = job['score_final_score']
        job_position = position
        position += 1
        empname = job['empname']
        if bidresult_ppc_u == None:
            bidresult_ppc_u = 0.0            
        if job_id == clicked_job_id:
            #print(f"Clicked Job: {job['title']} ")
            clicked = 1
        else:
            clicked = 0
        result = { 'job_id': job_id, 'job_title': job_title, 'bidresult_ppc_u': bidresult_ppc_u, 'job_position': job_position, 'score_final_score': score_final_score, 'empname': empname, 'clicked': clicked, 'search_type': search_type}
        results.append(result)
    # Sort results by score_final_score in descending order
    results.sort(key=lambda x: x['score_final_score'], reverse=True)

    # Update job_position based on the new sorted order
    for position, result in enumerate(results):
        result['job_position'] = position
    
    query_results = []
    query_result = { 'search_id': search_id, 'partner': partner, 'radius': radius, 'min_ppcu': min_ppcu, 'location': location, 'keyword': keyword, 'num_jobs_printed': num_jobs_printed, 'results': results}
    query_results_clicks_list.append(query_result)
    
    #if row.Index >= 10:
    #    break

# Write query results to a JSONL file (one JSON object per line)
with open('query_results_clicks.jsonl', 'w') as f:
    for query_result in query_results_clicks_list:
        # Convert each query result to JSON and write as a single line
        json_line = json.dumps(query_result)
        f.write(json_line + '\n')

print(f"Successfully wrote {len(query_results_clicks_list)} query results to query_results_clicks.jsonl")

import json
from tqdm import tqdm

def load_query_results_clicks(file_path="query_results_clicks.jsonl"):
    """
    Load query results clicks data from a JSONL file.
    
    Args:
        file_path (str): Path to the JSONL file
        
    Returns:
        list: List of query result dictionaries
    """
    query_results_clicks_list = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_number, line in enumerate(tqdm(f, desc="Loading query results")):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue
                
                try:
                    query_result = json.loads(line)
                    query_results_clicks_list.append(query_result)
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON at line {line_number+1}: {e}")
                    print(f"Line content: {line[:100]}...")  # Print first 100 chars of problematic line
    except FileNotFoundError:
        print(f"File not found: {file_path}")
    except Exception as e:
        print(f"Error reading file: {e}")
    
    print(f"Successfully loaded {len(query_results_clicks_list)} query results from {file_path}")
    return query_results_clicks_list

# Load the query results clicks data
query_results_clicks_list = load_query_results_clicks()

zero_min_ppcu_count = 0
organic_job_click_count = 0
for query in query_results_clicks_list:
    #print(query)
    if query['min_ppcu'] == 0:
        zero_min_ppcu_count += 1
    for result in query['results']:
        #print(result['bidresult_ppc_u'])
        if result['clicked'] == 0:
            continue                    
        if result['bidresult_ppc_u'] <= 0.000001:
            #print(result) 
            organic_job_click_count += 1            
        if float(result['bidresult_ppc_u']) < float(query['min_ppcu']):
            print(f"result['bidresult_ppc_u']: {result['bidresult_ppc_u']}, query['min_ppcu']: {query['min_ppcu']}")
print(f"zero_min_ppcu_count: {zero_min_ppcu_count}")    
print(f"organic_job_click_count: {organic_job_click_count}")





for query_result in query_results_clicks_list:
    keyword=query_result['keyword']
    location=query_result['location']

with open('publisher_queries.csv', 'w') as f:
    for query_result in query_results_clicks_list:
        keyword=query_result['keyword']
        location=query_result['location']
        csv_line = keyword + "," + location
        f.write(csv_line + '\n')

def run_llm_rater_old(query_results_clicks_list_batched):
    # Lets do LLLM rating
    count = 0
    count_relevant = 0
    click_count = 0
    for query_result in query_results_clicks_list_batched:
        keyword=query_result['keyword']
        location=query_result['location']
        min_ppcu=query_result['min_ppcu']
        radius=query_result['radius']
        for result in query_result['results']:
            job_id = result['job_id']
            job_title = result['job_title']
            job_position = result['job_position']
            score_final_score = result['score_final_score']
            empname = result['empname']
            clicked = result['clicked']
            search_type = result['search_type']
            job = job_title + " at " + empname
            if (clicked == 1):
                relevance_response = run_llm_judge((job_position+1),LLMS_AS_JUDGE.LLAMA_3_3_70B, keyword,"",job, min_ppcu, radius=radius)
                if (relevance_response == None):
                    relevancy = False
                else:
                    relevancy = relevance_response["Relevancy"]
                    reasoning = relevance_response["Reasoning"]
                
                print(f"Query: {keyword}, Job: {job_title}, Relevant: {relevancy}, Reasoning: {reasoning}")
                if (relevancy == True):
                    count_relevant += 1
                click_count += 1            
        count += 1
        if count > 1000:
            break
    prob_relevant = (count_relevant / click_count) * 100.0
    print(f"Total clicks: {click_count}, query count: {count}")
    print(f"Probability of relevant: {prob_relevant:.2f}")    


import concurrent.futures
from tqdm import tqdm
import math
import json
import copy

def run_parallel_llm_rating(query_results_clicks_list, output_file="query_results_clicks_with_relevance.jsonl", batch_size=1000, max_workers=8):
    """
    Parallelize the LLM rating process using ThreadPoolExecutor and store results.
    
    Args:
        query_results_clicks_list: List of query results to process
        output_file: Path to save the updated results
        batch_size: Number of queries per batch
        max_workers: Maximum number of concurrent threads
        
    Returns:
        dict: Aggregated results with relevancy statistics
    """
    # Create a deep copy to avoid modifying the original data
    query_results_copy = copy.deepcopy(query_results_clicks_list)
    
    # Split the data into batches
    total_queries = len(query_results_copy)
    num_batches = math.ceil(total_queries / batch_size)
    batches = []
    
    for i in range(num_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, total_queries)
        batches.append(query_results_copy[start_idx:end_idx])
    
    print(f"Split data into {num_batches} batches of up to {batch_size} queries each")
    
    # Results aggregation
    total_count = 0
    total_relevant = 0
    total_clicks = 0
    processed_results = []
    
    # Process batches in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all batches to the executor
        future_to_batch = {executor.submit(run_llm_rater_with_storage, batch): i for i, batch in enumerate(batches)}
        
        # Process results as they complete
        for future in tqdm(concurrent.futures.as_completed(future_to_batch), 
                          total=len(future_to_batch), 
                          desc="Processing batches"):
            batch_idx = future_to_batch[future]
            try:
                # Get the results from this batch
                batch_results, batch_count, batch_relevant, batch_clicks = future.result()
                
                # Aggregate results
                processed_results.extend(batch_results)
                total_count += batch_count
                total_relevant += batch_relevant
                total_clicks += batch_clicks
                
                print(f"Batch {batch_idx+1}/{num_batches} completed: {batch_clicks} clicks, {batch_relevant} relevant")
            except Exception as e:
                print(f"Batch {batch_idx+1} generated an exception: {e}")
    
    # Calculate overall relevancy percentage
    relevancy_percentage = (total_relevant / total_clicks * 100) if total_clicks > 0 else 0
    
    # Write the updated results to file
    with open(output_file, 'w', encoding='utf-8') as f:
        for result in processed_results:
            f.write(json.dumps(result) + '\n')
    
    print(f"\nUpdated results written to {output_file}")
    print(f"\nOverall Results:")
    print(f"Total queries processed: {total_count}")
    print(f"Total clicks analyzed: {total_clicks}")
    print(f"Total relevant clicks: {total_relevant}")
    print(f"Overall relevancy percentage: {relevancy_percentage:.2f}%")
    
    return {
        "total_queries": total_count,
        "total_clicks": total_clicks,
        "relevant_clicks": total_relevant,
        "relevancy_percentage": relevancy_percentage,
        "processed_results": processed_results
    }

# Modified function to store relevance data in the query results
def run_llm_rater_with_storage(query_results_clicks_list_batched):
    # Lets do LLM rating
    count = 0
    count_relevant = 0
    click_count = 0
    
    for query_result in query_results_clicks_list_batched:
        keyword = query_result['keyword']
        location = query_result['location']
        min_ppcu = query_result['min_ppcu']
        radius = query_result['radius']
        
        for result in query_result['results']:
            job_id = result['job_id']
            job_title = result['job_title']
            job_position = result['job_position']
            score_final_score = result['score_final_score']
            empname = result['empname']
            clicked = result['clicked']
            search_type = result['search_type']
            job = job_title + " at " + empname
            
            # Initialize relevance fields
            if 'relevance' not in result:
                result['relevance'] = {
                    'is_relevant': None,
                    'reasoning': None,
                    'llm_model': None,
                    'processed': False
                }
            
            if clicked == 1 and not result['relevance']['processed']:
                relevance_response = run_llm_judge(
                    (job_position+1), 
                    LLMS_AS_JUDGE.LLAMA_3_3_70B, 
                    keyword, 
                    "", 
                    job, 
                    min_ppcu, 
                    radius=radius
                )
                
                if relevance_response is None:
                    relevancy = False
                    reasoning = "Failed to get response from LLM"
                else:
                    relevancy = relevance_response["Relevancy"]
                    reasoning = relevance_response["Reasoning"]
                
                # Store relevance data in the result object
                result['relevance'] = {
                    'is_relevant': relevancy,
                    'reasoning': reasoning,
                    'llm_model': 'LLAMA_3_3_70B',
                    'processed': True,
                    'search_type': search_type
                }
                
                #print(f"Query: {keyword}, Job: {job_title}, Relevant: {relevancy}, Reasoning: {reasoning[:100]}...")
                
                if relevancy:
                    count_relevant += 1
                click_count += 1
        
        count += 1
        if count > 1000:
            break
    
    # Return both the updated data and the counts
    return query_results_clicks_list_batched, count, count_relevant, click_count



# Run the parallel processing
results = run_parallel_llm_rating(
    query_results_clicks_list, 
    output_file="query_results_clicks_with_relevance.jsonl",
    batch_size=1000, 
    max_workers=8
)

print(results)

import json
from tqdm import tqdm

def load_query_results_with_relevance(file_path="query_results_clicks_with_relevance.jsonl"):
    """
    Load query results with relevance data from a JSONL file.
    
    Args:
        file_path (str): Path to the JSONL file containing query results with relevance data
        
    Returns:
        list: List of query result dictionaries with relevance data
        dict: Summary statistics about the loaded data
    """
    query_results_list = []
    
    # Statistics counters
    total_queries = 0
    total_results = 0
    total_clicks = 0
    total_processed = 0
    total_relevant = 0
    
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_number, line in enumerate(tqdm(f, desc="Loading query results with relevance")):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue
                
                try:
                    query_result = json.loads(line)
                    query_results_list.append(query_result)
                    
                    # Update statistics
                    total_queries += 1
                    results_count = len(query_result.get('results', []))
                    total_results += results_count
                    
                    # Count clicks and relevance data
                    for result in query_result.get('results', []):
                        if result.get('clicked') == 1:
                            total_clicks += 1
                            
                            relevance_data = result.get('relevance', {})
                            if relevance_data.get('processed', False):
                                total_processed += 1
                                if relevance_data.get('is_relevant', False):
                                    total_relevant += 1
                    
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON at line {line_number+1}: {e}")
                    print(f"Line content: {line[:100]}...")  # Print first 100 chars of problematic line
    except FileNotFoundError:
        print(f"File not found: {file_path}")
    except Exception as e:
        print(f"Error reading file: {e}")
    
    # Calculate statistics
    relevancy_percentage = (total_relevant / total_clicks * 100) if total_clicks > 0 else 0
    processed_percentage = (total_processed / total_clicks * 100) if total_clicks > 0 else 0
    
    # Create summary statistics
    stats = {
        "total_queries": total_queries,
        "total_results": total_results,
        "total_clicks": total_clicks,
        "processed_clicks": total_processed,
        "relevant_clicks": total_relevant,
        "relevancy_percentage": relevancy_percentage,
        "processed_percentage": processed_percentage
    }
    
    print(f"Successfully loaded {total_queries} queries from {file_path}")
    print(f"Total results: {total_results}")
    print(f"Total clicks: {total_clicks}")
    print(f"Processed clicks: {total_processed} ({processed_percentage:.2f}%)")
    print(f"Relevant clicks: {total_relevant} ({relevancy_percentage:.2f}%)")
    
    return query_results_list, stats



# Load the query results with relevance data
query_results_with_relevance, relevance_stats = load_query_results_with_relevance()

params_query = """
SELECT * 
FROM "ml"."prints_clicks_crawledoutput" 
WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
AND length(keyword) > :min_keyword_length
AND has_click > :has_click
AND has_apply > :has_apply
LIMIT :limit
"""

parameters = {
    'min_keyword_length': 1,
    'has_click': 0,
    'has_apply': 0,
    'limit': 20000
}

apply_results_df = athena.query_to_dataframe(params_query, parameters=parameters)

query_results_apply_list = []
query_job_id_apply_dict = {}

for row in tqdm(apply_results_df.itertuples()):  
    search_request = json.loads(row.request)
    #pprint(search_request)
    search_id = row.search_id
    #print(f"partner: {partner}, location: {location}, keyword: {keyword}, num_jobs_printed: {num_jobs_printed}")
    apply_job_id = int(row.clicked_job_id)
    key = search_id + "_" + str(apply_job_id)
    query_job_id_apply_dict[key] = apply_job_id
    


apply_count = 0
apply_relevance_count = 0
for query_result in query_results_with_relevance:
    keyword = query_result['keyword']
    location = query_result['location']
    min_ppcu = query_result['min_ppcu']
    radius = query_result['radius']
    
    for result in query_result['results']:
        job_id = result['job_id']
        job_title = result['job_title']
        job_position = result['job_position']
        score_final_score = result['score_final_score']
        empname = result['empname']
        clicked = result['clicked']
        search_type = result['search_type']
        job = job_title + " at " + empname
        key = query_result['search_id'] + "_" + str(job_id)
        if key in query_job_id_apply_dict:
            result['applied'] = 1
        else:
            result['applied'] = 0
        
        if result['applied'] == 1:
            apply_count += 1
            if result['relevance']['is_relevant']:
                apply_relevance_count += 1
print(f"Total applies: {apply_count}")
print(f"Relevant applies: {apply_relevance_count}")
relevancy_percentage = (apply_relevance_count / apply_count * 100) if apply_count > 0 else 0
print(f"Relevancy percentage: {relevancy_percentage:.2f}%")



def load_playground_queries(filename="playground_queries.csv"):
    """
    Loads playground queries from a CSV file into a dictionary.
    Expected CSV format: keyword,location,country
    
    Args:
        filename (str): Path to the CSV file containing playground queries
        
    Returns:
        list[dict]: List of dictionaries containing query parameters
    """
    queries = []
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                keyword = row['keyword']
                config_retriever_name = row["config_retriever_name"]
                structured_data = json.loads(row['structured_data'])
                location = structured_data['geo']['formatted_address']
                countrycode = structured_data['geo']['countrycode']            
                query = {
                    'keyword': keyword.strip(),
                    'location': location.strip(),
                    'country': countrycode.strip(),
                }
                queries.append(query)
        return queries
    except Exception as e:
        print(f"Error loading queries: {str(e)}")
        return []

# Load the query results clicks data
query_results_clicks_list = load_playground_queries('/Users/<USER>/talent/sandbox/playground/query_classification/playground_queries.csv')

import concurrent.futures
from tqdm import tqdm
import math
import json
import csv

def run_parallel_query_analysis(query_results_clicks_list, output_file="query_analysis.csv", 
                               batch_size=1000, max_workers=8, max_queries=None):
    """
    Parallelize the LLM query analysis using ThreadPoolExecutor.
    
    Args:
        query_results_clicks_list: List of query results to process
        output_file: Path to save the analysis results
        batch_size: Number of queries per batch
        max_workers: Maximum number of concurrent threads
        max_queries: Maximum number of queries to process (None for all)
        
    Returns:
        dict: Dictionary mapping keywords to their analysis results
    """
    # Extract unique keywords to avoid duplicate processing
    unique_queries = []
    seen_keywords = set()
    
    for query_result in query_results_clicks_list:
        keyword = query_result.get('keyword', '')
        if not keyword or keyword in seen_keywords:
            continue
            
        seen_keywords.add(keyword)
        unique_queries.append({
            'keyword': keyword,
            'location': query_result.get('location', '')
        })
    
    # Apply max_queries limit if specified
    if max_queries is not None and max_queries > 0:
        unique_queries = unique_queries[:max_queries]
        print(f"Limiting to {len(unique_queries)} queries (max_queries={max_queries})")
    else:
        print(f"Found {len(unique_queries)} unique keywords to analyze")
    
    # Split the queries into batches
    total_queries = len(unique_queries)
    num_batches = math.ceil(total_queries / batch_size)
    batches = []
    
    for i in range(num_batches):
        start_idx = i * batch_size
        end_idx = min((i + 1) * batch_size, total_queries)
        batches.append(unique_queries[start_idx:end_idx])
    
    print(f"Split data into {num_batches} batches of up to {batch_size} queries each")
    
    # Results dictionary
    query_analysis_dict = {}
    processed_count = 0
    
    # Define a worker function to process a single query
    def process_query(query_data):
        keyword = query_data['keyword']
        try:
            analysis_response = run_llm_judge_on_query(keyword, LLMS_AS_JUDGE.LLAMA_3_3_70B)
            analysis = json.loads(analysis_response)
            #print(f"keyword: {keyword}, analysis: {analysis}")
            # Handle None or missing fields
            if analysis is None:
                analysis = {}
                
            # Ensure all required fields exist
            for field in ['query_type', 'soc_major_group', 'soc_code', 'soc_title']:
                if field not in analysis or not analysis[field]:
                    analysis[field] = ''
                    
            return keyword, analysis
        except Exception as e:
            print(e, analysis_response)
            return keyword, {
                'query_type': '',
                'soc_major_group': '',
                'soc_code': '',
                'soc_title': ''
            }
    
    # Process batches until we reach max_queries
    for batch_idx, batch in enumerate(batches):
        print(f"Processing batch {batch_idx+1}/{num_batches} with {len(batch)} queries")
        
        # Process batch in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all queries in this batch
            future_to_query = {executor.submit(process_query, query_data): query_data['keyword'] 
                              for query_data in batch}
            
            # Process results as they complete
            for future in tqdm(concurrent.futures.as_completed(future_to_query), 
                              total=len(future_to_query), 
                              desc=f"Batch {batch_idx+1}"):
                try:
                    keyword, analysis = future.result()
                    #print(analysis)
                    query_analysis_dict[keyword] = analysis
                    processed_count += 1
                except Exception as e:
                    keyword = future_to_query[future]
                    print(f"Error processing {keyword}: {e}")
    
    # Write results to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(['keyword', 'query_type', 'soc_major_group', 'soc_code', 'soc_title'])
        for keyword, analysis in query_analysis_dict.items():
            writer.writerow([
                keyword, 
                analysis.get('query_type', ''), 
                analysis.get('soc_major_group', ''), 
                analysis.get('soc_code', ''), 
                analysis.get('soc_title', '')
            ])
    
    print(f"Successfully analyzed {len(query_analysis_dict)} queries")
    print(f"Results written to {output_file}")
    
    return query_analysis_dict



query_analysis_dict = run_parallel_query_analysis(
    query_results_clicks_list,
    output_file="publisher_query_analysis.csv",
    batch_size=1000,
    max_workers=8,
    max_queries=20000  # Set a limit to process only 5000 queries
)

# Run the parallel processing with a limit of 5000 queries
query_analysis_dict = run_parallel_query_analysis(
    query_results_clicks_list,
    output_file="query_analysis.csv",
    batch_size=1000,
    max_workers=8,
    max_queries=20000  # Set a limit to process only 5000 queries
)


pprint(query_analysis_dict)

params_query = """
SELECT * 
FROM "ml"."prints_clicks_crawledoutput" 
WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
AND length(keyword) > :min_keyword_length
AND has_click > :has_click
ORDER BY search_date desc
LIMIT :limit
"""

parameters = {
    'min_keyword_length': 1,
    'has_click': 0,
    'limit': 20000
}

click_results_df = athena.query_to_dataframe(params_query, parameters=parameters)

params_query = """
SELECT *
FROM "ml"."prints_clicks_crawledoutput" TABLESAMPLE BERNOULLI(10)
WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
AND length(keyword) > :min_keyword_length AND has_click <= 0
ORDER BY search_date desc
LIMIT :limit;
"""
parameters = {
    'min_keyword_length': 1,
    #'has_click': 0,
    'limit': 100000
}

click_results_df = athena.query_to_dataframe(params_query, parameters=parameters)

query_results_zero_clicks_list = []

for row in tqdm(click_results_df.itertuples()):  
    search_request = json.loads(row.request)
    #pprint(search_request)
    search_id = row.search_id
    partner = search_request['config']['partner']
    if 'formatted_address' not in search_request['geo']:
        continue
    location = search_request['geo']['formatted_address']
    keyword = search_request['keyword']
    radius = search_request['radius']
    if 'minppcu' in search_request['config']:
        min_ppcu = search_request['config']['minppcu']
    else:
        min_ppcu = 0
    
    num_jobs_printed = len(jobs_printed)    
    #print(f"partner: {partner}, location: {location}, keyword: {keyword}, num_jobs_printed: {num_jobs_printed}")
    if (row.jobs == None):
        continue    
    jobs_printed = parse_custom_format(row.jobs)
    pprint(search_request)
    pprint(jobs_printed)
    break
    results = []
    position = 0
    for job in jobs_printed:
        #pprint(job)
        #break
        if 'id' not in job:
            continue        
        job_id = int(job['id'])
        job_title = job['title']
        bidresult_ppc_u = job['bidresult_ppc_u']
        search_type = job['query_source_querytype']
        score_final_score = float(job['score_final_score'])
        job_position = position
        position += 1
        empname = job['empname']
        clicked = 0
        result = { 'job_id': job_id, 'job_title': job_title, 'bidresult_ppc_u': bidresult_ppc_u, 'job_position': job_position, 'score_final_score': score_final_score, 'empname': empname, 'clicked': clicked, 'search_type': search_type}
        results.append(result)
    # Sort results by score_final_score in descending order
    results.sort(key=lambda x: x['score_final_score'], reverse=True)

    # Update job_position based on the new sorted order
    for position, result in enumerate(results):
        result['job_position'] = position
    
    query_results = []
    query_result = { 'search_id': search_id, 'partner': partner, 'radius': radius, 'min_ppcu': min_ppcu, 'location': location, 'keyword': keyword, 'num_jobs_printed': num_jobs_printed, 'results': results}
    query_results_zero_clicks_list.append(query_result)
    
    #if row.Index >= 10:
    #    break

zero_clicked_result_type_count_dict = {}

for query_result in query_results_zero_clicks_list:
    keyword=query_result['keyword']
    location=query_result['location']
    for result in query_result['results']:
        job_id = result['job_id']
        job_title = result['job_title']
        job_position = result['job_position']
        score_final_score = result['score_final_score']
        empname = result['empname']
        clicked = result['clicked']
        search_type = result['search_type']
        if search_type == 'DSL - elastic':
            continue
        if search_type == 'DSL - opensearch':
            continue
        if search_type not in zero_clicked_result_type_count_dict:
            zero_clicked_result_type_count_dict[search_type] = 0
        zero_clicked_result_type_count_dict[search_type] += 1
pprint(zero_clicked_result_type_count_dict)
clicked_result_type_prob_dict = {k: v/sum(zero_clicked_result_type_count_dict.values()) * 100.0 for k, v in zero_clicked_result_type_count_dict.items()}
pprint(clicked_result_type_prob_dict)

pprint(query_results_zero_clicks_list)

params_query = """
SELECT *
FROM "ml"."prints_clicks_crawledoutput" TABLESAMPLE BERNOULLI(10)
WHERE year='2025' AND month='04' AND day='01' AND hour = '16'
AND length(keyword) > :min_keyword_length AND has_click <= 0
ORDER BY search_date desc
LIMIT :limit;
"""
parameters = {
    'min_keyword_length': 1,
    #'has_click': 0,
    'limit': 100000
}

click_results_df = athena.query_to_dataframe(params_query, parameters=parameters)