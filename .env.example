# AI Job Search Chatbot Environment Configuration

# =============================================================================
# Required Configuration
# =============================================================================

# OpenAI API Key (Required for AI conversations)
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# Local OpenSearch Configuration (Default)
# =============================================================================
# These are automatically configured for local Docker setup
# No changes needed for local development

# OPENSEARCH_HOST=opensearch
# OPENSEARCH_PORT=9200
# OPENSEARCH_USERNAME=admin
# OPENSEARCH_PASSWORD=admin
# OPENSEARCH_USE_SSL=false
# OPENSEARCH_INDEX=jobs

# =============================================================================
# External OpenSearch Configuration (Optional)
# =============================================================================
# Uncomment and configure these for external OpenSearch clusters

# Full OpenSearch URL (recommended for external clusters)
# OPENSEARCH_URL=https://your-opensearch-domain.com

# Basic Authentication
# OPENSEARCH_USERNAME=your_username
# OPENSEARCH_PASSWORD=your_password

# SSL Configuration
# OPENSEARCH_USE_SSL=true
# OPENSEARCH_VERIFY_CERTS=true

# AWS OpenSearch Service Configuration
# OPENSEARCH_USE_AWS_AUTH=true
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Index Configuration
# OPENSEARCH_INDEX=jobs

# =============================================================================
# Application Configuration (Optional)
# =============================================================================

# Backend Configuration
# PORT=8000
# DEBUG=true

# Frontend Configuration
# NEXT_PUBLIC_CHATBOT_API_URL=http://localhost:8000
# NEXT_PUBLIC_CHATBOT_WS_URL=ws://localhost:8000

# =============================================================================
# Quick Setup Instructions
# =============================================================================
# 1. Copy this file: cp .env.example .env
# 2. Add your OpenAI API key above
# 3. For local setup: docker-compose -f docker-compose.chatbot.yml up -d
# 4. For external OpenSearch: uncomment and configure the external section
# =============================================================================
